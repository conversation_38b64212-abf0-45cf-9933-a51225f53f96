[29-May-2025 06:11:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[29-May-2025 06:16:44 UTC] PHP Fatal error:  During class fetch: Uncaught ParseError: syntax error, unexpected token "return" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php:339
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#1 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php(19): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#2 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php(27): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#5 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#6 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#7 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#8 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#9 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#10 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#11 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#12 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#13 {main} in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php on line 19
[29-May-2025 06:22:47 UTC] PHP Fatal error:  During class fetch: Uncaught ParseError: Unclosed '[' on line 348 does not match '}' in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php:351
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#1 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php(19): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#2 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php(27): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#5 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#6 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#7 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#8 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#9 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#10 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#11 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#12 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#13 {main} in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php on line 19
[29-May-2025 08:19:12 UTC] Automatic updates starting...
[29-May-2025 08:19:13 UTC]   Automatic plugin updates starting...
[29-May-2025 08:19:13 UTC]   Automatic plugin updates complete.
[29-May-2025 08:19:14 UTC]   Automatic theme updates starting...
[29-May-2025 08:19:14 UTC]   Automatic theme updates complete.
[29-May-2025 08:19:14 UTC] Automatic updates complete.
[29-May-2025 09:06:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 02:32:36 UTC] PHP Warning:  wp_version_check(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 02:32:37 UTC] Automatic updates starting...
[01-Jun-2025 02:32:38 UTC]   Automatic plugin updates starting...
[01-Jun-2025 02:32:38 UTC]   Automatic plugin updates complete.
[01-Jun-2025 02:32:39 UTC]   Automatic theme updates starting...
[01-Jun-2025 02:32:39 UTC]   Automatic theme updates complete.
[01-Jun-2025 02:32:39 UTC] Automatic updates complete.
[01-Jun-2025 02:32:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 03:39:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 08:20:19 UTC] Automatic updates starting...
[01-Jun-2025 08:20:20 UTC]   Automatic plugin updates starting...
[01-Jun-2025 08:20:20 UTC]   Automatic plugin updates complete.
[01-Jun-2025 08:20:21 UTC]   Automatic theme updates starting...
[01-Jun-2025 08:20:21 UTC]   Automatic theme updates complete.
[01-Jun-2025 08:20:21 UTC] Automatic updates complete.
[01-Jun-2025 09:01:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[02-Jun-2025 03:10:54 UTC] Automatic updates starting...
[02-Jun-2025 03:10:55 UTC]   Automatic plugin updates starting...
[02-Jun-2025 03:10:55 UTC]   Automatic plugin updates complete.
[02-Jun-2025 03:10:56 UTC]   Automatic theme updates starting...
[02-Jun-2025 03:10:56 UTC]   Automatic theme updates complete.
[02-Jun-2025 03:10:56 UTC] Automatic updates complete.
[02-Jun-2025 08:18:55 UTC] Automatic updates starting...
[02-Jun-2025 08:18:56 UTC]   Automatic plugin updates starting...
[02-Jun-2025 08:18:56 UTC]   Automatic plugin updates complete.
[02-Jun-2025 08:18:57 UTC]   Automatic theme updates starting...
[02-Jun-2025 08:18:57 UTC]   Automatic theme updates complete.
[02-Jun-2025 08:18:57 UTC] Automatic updates complete.
[02-Jun-2025 08:59:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[02-Jun-2025 21:46:38 UTC] Automatic updates starting...
[02-Jun-2025 21:46:39 UTC]   Automatic plugin updates starting...
[02-Jun-2025 21:46:39 UTC]   Automatic plugin updates complete.
[02-Jun-2025 22:03:48 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[02-Jun-2025 22:03:49 UTC]   Automatic theme updates starting...
[02-Jun-2025 22:03:49 UTC]   Automatic theme updates complete.
[02-Jun-2025 22:03:49 UTC] Automatic updates complete.
[02-Jun-2025 22:18:57 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 03:39:50 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 03:40:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 03:40:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 04:52:15 UTC] PHP Fatal error:  During class fetch: Uncaught ParseError: Unclosed '[' on line 375 does not match '}' in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php:384
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#1 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php(19): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#2 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php(27): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#5 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#6 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#7 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#8 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#9 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#10 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#11 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#12 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#13 {main} in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php on line 19
[03-Jun-2025 05:15:51 UTC] PHP Fatal error:  Cannot redeclare Better_Payment\Lite\Traits\Helper::get_currency_symbols_list() in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php on line 500
[03-Jun-2025 05:17:52 UTC] PHP Fatal error:  Cannot redeclare Better_Payment\Lite\Traits\Helper::get_currency_symbols_list() in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php on line 480
[03-Jun-2025 05:19:53 UTC] PHP Fatal error:  Cannot redeclare Better_Payment\Lite\Traits\Helper::get_currency_symbols_list() in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php on line 475
[03-Jun-2025 05:21:54 UTC] PHP Fatal error:  Cannot redeclare Better_Payment\Lite\Traits\Helper::get_currency_symbols_list() in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php on line 500
[03-Jun-2025 05:23:55 UTC] PHP Fatal error:  Cannot redeclare Better_Payment\Lite\Traits\Helper::get_currency_symbols_list() in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php on line 459
[03-Jun-2025 05:25:56 UTC] PHP Fatal error:  Cannot redeclare Better_Payment\Lite\Traits\Helper::get_currency_symbols_list() in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php on line 444
[03-Jun-2025 06:30:33 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[03-Jun-2025 06:30:33 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[03-Jun-2025 06:30:33 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[03-Jun-2025 06:30:33 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[03-Jun-2025 08:17:48 UTC] Automatic updates starting...
[03-Jun-2025 08:17:49 UTC]   Automatic plugin updates starting...
[03-Jun-2025 08:17:49 UTC]   Automatic plugin updates complete.
[03-Jun-2025 08:17:50 UTC]   Automatic theme updates starting...
[03-Jun-2025 08:17:50 UTC]   Automatic theme updates complete.
[03-Jun-2025 08:17:50 UTC] Automatic updates complete.
[03-Jun-2025 11:01:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[04-Jun-2025 03:27:13 UTC] Automatic updates starting...
[04-Jun-2025 03:27:14 UTC]   Automatic plugin updates starting...
[04-Jun-2025 03:27:14 UTC]   Automatic plugin updates complete.
[04-Jun-2025 03:27:15 UTC]   Automatic theme updates starting...
[04-Jun-2025 03:27:15 UTC]   Automatic theme updates complete.
[04-Jun-2025 03:27:15 UTC] Automatic updates complete.
[04-Jun-2025 08:36:13 UTC] Automatic updates starting...
[04-Jun-2025 08:36:14 UTC]   Automatic plugin updates starting...
[04-Jun-2025 08:36:14 UTC]   Automatic plugin updates complete.
[04-Jun-2025 08:36:15 UTC]   Automatic theme updates starting...
[04-Jun-2025 08:36:15 UTC]   Automatic theme updates complete.
[04-Jun-2025 08:36:15 UTC] Automatic updates complete.
[04-Jun-2025 08:59:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[04-Jun-2025 10:05:07 UTC] PHP Warning:  Undefined variable $bpc_goal_percentage_formatted in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 143
[04-Jun-2025 23:45:18 UTC] Automatic updates starting...
[04-Jun-2025 23:45:19 UTC]   Automatic plugin updates starting...
[04-Jun-2025 23:45:19 UTC]   Automatic plugin updates complete.
[04-Jun-2025 23:45:20 UTC]   Automatic theme updates starting...
[04-Jun-2025 23:45:20 UTC]   Automatic theme updates complete.
[04-Jun-2025 23:45:20 UTC] Automatic updates complete.
[05-Jun-2025 00:02:49 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 04:48:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 04:48:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 10:23:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 10:38:37 UTC] PHP Warning:  get_core_checksums(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 10:38:40 UTC] Automatic updates starting...
[05-Jun-2025 10:38:41 UTC]   Automatic plugin updates starting...
[05-Jun-2025 10:38:41 UTC]   Automatic plugin updates complete.
[05-Jun-2025 10:38:42 UTC]   Automatic theme updates starting...
[05-Jun-2025 10:38:42 UTC]   Automatic theme updates complete.
[05-Jun-2025 10:38:42 UTC] Automatic updates complete.
[05-Jun-2025 10:54:05 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 10:54:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 10:54:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 14:24:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 19:24:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 23:50:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[05-Jun-2025 23:50:14 UTC] Automatic updates starting...
[06-Jun-2025 00:06:37 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 00:06:37 UTC]   Automatic plugin updates starting...
[06-Jun-2025 00:06:37 UTC]   Automatic plugin updates complete.
[06-Jun-2025 00:06:38 UTC]   Automatic theme updates starting...
[06-Jun-2025 00:06:38 UTC]   Automatic theme updates complete.
[06-Jun-2025 00:06:38 UTC] Automatic updates complete.
[06-Jun-2025 05:23:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 05:23:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 11:01:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 11:01:26 UTC] Automatic updates starting...
[06-Jun-2025 11:01:26 UTC]   Automatic plugin updates starting...
[06-Jun-2025 11:01:26 UTC]   Automatic plugin updates complete.
[06-Jun-2025 11:01:27 UTC]   Automatic theme updates starting...
[06-Jun-2025 11:01:27 UTC]   Automatic theme updates complete.
[06-Jun-2025 11:01:27 UTC] Automatic updates complete.
[06-Jun-2025 11:17:15 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 11:17:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 14:02:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 14:02:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 17:38:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 17:53:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 22:43:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[06-Jun-2025 23:00:59 UTC] Automatic updates starting...
[06-Jun-2025 23:01:00 UTC]   Automatic plugin updates starting...
[06-Jun-2025 23:01:00 UTC]   Automatic plugin updates complete.
[06-Jun-2025 23:01:01 UTC]   Automatic theme updates starting...
[06-Jun-2025 23:01:01 UTC]   Automatic theme updates complete.
[06-Jun-2025 23:01:01 UTC] Automatic updates complete.
[06-Jun-2025 23:18:26 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[07-Jun-2025 03:13:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[07-Jun-2025 08:51:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[07-Jun-2025 08:51:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[07-Jun-2025 08:51:15 UTC] Automatic updates starting...
[07-Jun-2025 08:51:16 UTC]   Automatic plugin updates starting...
[07-Jun-2025 08:51:16 UTC]   Automatic plugin updates complete.
[07-Jun-2025 08:51:17 UTC]   Automatic theme updates starting...
[07-Jun-2025 08:51:17 UTC]   Automatic theme updates complete.
[07-Jun-2025 08:51:17 UTC] Automatic updates complete.
[07-Jun-2025 09:09:14 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[07-Jun-2025 14:00:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[07-Jun-2025 14:00:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[07-Jun-2025 19:44:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 01:27:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 01:27:34 UTC] Automatic updates starting...
[08-Jun-2025 01:27:35 UTC]   Automatic plugin updates starting...
[08-Jun-2025 01:27:35 UTC]   Automatic plugin updates complete.
[08-Jun-2025 01:36:11 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 01:36:12 UTC]   Automatic theme updates starting...
[08-Jun-2025 01:36:12 UTC]   Automatic theme updates complete.
[08-Jun-2025 01:36:12 UTC] Automatic updates complete.
[08-Jun-2025 07:33:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 09:51:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 09:51:26 UTC] Automatic updates starting...
[08-Jun-2025 09:51:27 UTC]   Automatic plugin updates starting...
[08-Jun-2025 09:51:27 UTC]   Automatic plugin updates complete.
[08-Jun-2025 09:51:28 UTC]   Automatic theme updates starting...
[08-Jun-2025 09:51:28 UTC]   Automatic theme updates complete.
[08-Jun-2025 09:51:28 UTC] Automatic updates complete.
[08-Jun-2025 09:51:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 09:52:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 09:53:26 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 09:55:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 09:57:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 09:59:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:01:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:03:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:05:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:07:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:09:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:11:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:13:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:15:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:17:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:19:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:21:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:23:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:25:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:27:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:29:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:31:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:33:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:35:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:37:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:39:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:41:50 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:43:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:45:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:47:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:51:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:53:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:55:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:57:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 10:59:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 11:01:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 11:03:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[08-Jun-2025 11:38:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[11-Jun-2025 03:47:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[11-Jun-2025 03:47:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[11-Jun-2025 03:47:22 UTC] Automatic updates starting...
[11-Jun-2025 03:47:25 UTC]   Automatic plugin updates starting...
[11-Jun-2025 03:47:25 UTC]   Automatic plugin updates complete.
[11-Jun-2025 03:47:29 UTC]   Automatic theme updates starting...
[11-Jun-2025 03:47:29 UTC]   Automatic theme updates complete.
[11-Jun-2025 03:47:29 UTC] Automatic updates complete.
[11-Jun-2025 03:47:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[11-Jun-2025 07:03:08 UTC] PHP Warning:  Undefined variable $better_payment_helper in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 5
[11-Jun-2025 07:03:15 UTC] PHP Warning:  Undefined variable $better_payment_helper in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 5
[11-Jun-2025 07:03:38 UTC] PHP Warning:  Undefined variable $better_payment_helper in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 5
[11-Jun-2025 08:22:10 UTC] Automatic updates starting...
[11-Jun-2025 08:22:11 UTC]   Automatic plugin updates starting...
[11-Jun-2025 08:22:11 UTC]   Automatic plugin updates complete.
[11-Jun-2025 08:22:12 UTC]   Automatic theme updates starting...
[11-Jun-2025 08:22:12 UTC]   Automatic theme updates complete.
[11-Jun-2025 08:22:12 UTC] Automatic updates complete.
[11-Jun-2025 09:55:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[11-Jun-2025 21:38:44 UTC] Automatic updates starting...
[11-Jun-2025 21:38:45 UTC]   Automatic plugin updates starting...
[11-Jun-2025 21:38:45 UTC]   Automatic plugin updates complete.
[11-Jun-2025 21:38:46 UTC]   Automatic theme updates starting...
[11-Jun-2025 21:38:46 UTC]   Automatic theme updates complete.
[11-Jun-2025 21:38:46 UTC] Automatic updates complete.
[11-Jun-2025 21:56:17 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[12-Jun-2025 06:12:13 UTC] PHP Warning:  Undefined array key "better_payment_campaign_form_currency" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/Elementor/Fundraising_Campaign_Widget.php on line 3238
[12-Jun-2025 06:12:15 UTC] PHP Warning:  Undefined array key "better_payment_campaign_form_currency" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/Elementor/Fundraising_Campaign_Widget.php on line 3238
[12-Jun-2025 06:12:17 UTC] PHP Warning:  Undefined array key "better_payment_campaign_form_currency" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/Elementor/Fundraising_Campaign_Widget.php on line 3238
[12-Jun-2025 07:38:31 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[12-Jun-2025 07:38:31 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[12-Jun-2025 07:38:31 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[12-Jun-2025 07:38:31 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[12-Jun-2025 08:20:24 UTC] Automatic updates starting...
[12-Jun-2025 08:20:25 UTC]   Automatic plugin updates starting...
[12-Jun-2025 08:20:25 UTC]   Automatic plugin updates complete.
[12-Jun-2025 08:20:26 UTC]   Automatic theme updates starting...
[12-Jun-2025 08:20:26 UTC]   Automatic theme updates complete.
[12-Jun-2025 08:20:26 UTC] Automatic updates complete.
[15-Jun-2025 03:52:45 UTC] Automatic updates starting...
[15-Jun-2025 03:52:46 UTC]   Automatic plugin updates starting...
[15-Jun-2025 03:52:46 UTC]   Automatic plugin updates complete.
[15-Jun-2025 03:52:47 UTC]   Automatic theme updates starting...
[15-Jun-2025 03:52:47 UTC]   Automatic theme updates complete.
[15-Jun-2025 03:52:47 UTC] Automatic updates complete.
[15-Jun-2025 08:18:37 UTC] PHP Warning:  wp_version_check(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[16-Jun-2025 03:06:55 UTC] Automatic updates starting...
[16-Jun-2025 03:06:56 UTC]   Automatic plugin updates starting...
[16-Jun-2025 03:06:56 UTC]   Automatic plugin updates complete.
[16-Jun-2025 03:06:57 UTC]   Automatic theme updates starting...
[16-Jun-2025 03:06:57 UTC]   Automatic theme updates complete.
[16-Jun-2025 03:06:57 UTC] Automatic updates complete.
[16-Jun-2025 08:19:30 UTC] Automatic updates starting...
[16-Jun-2025 08:19:31 UTC]   Automatic plugin updates starting...
[16-Jun-2025 08:19:31 UTC]   Automatic plugin updates complete.
[16-Jun-2025 08:19:32 UTC]   Automatic theme updates starting...
[16-Jun-2025 08:19:32 UTC]   Automatic theme updates complete.
[16-Jun-2025 08:19:32 UTC] Automatic updates complete.
[16-Jun-2025 08:59:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[16-Jun-2025 21:56:35 UTC] PHP Warning:  wp_version_check(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[16-Jun-2025 21:56:36 UTC] Automatic updates starting...
[16-Jun-2025 21:56:37 UTC]   Automatic plugin updates starting...
[16-Jun-2025 21:56:37 UTC]   Automatic plugin updates complete.
[16-Jun-2025 21:56:37 UTC]   Automatic theme updates starting...
[16-Jun-2025 21:56:37 UTC]   Automatic theme updates complete.
[16-Jun-2025 21:56:37 UTC] Automatic updates complete.
[16-Jun-2025 22:11:48 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 03:54:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 03:54:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 03:54:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 03:56:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 03:56:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 03:58:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 03:58:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:00:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:00:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:02:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:02:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:04:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:04:46 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:06:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:06:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:08:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:08:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 04:09:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[17-Jun-2025 05:18:55 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:18:55 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:19:58 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:19:58 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:19:58 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:19:58 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:20:37 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:20:37 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:20:37 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:20:37 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:20:38 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:20:38 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:20:38 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:20:38 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:20:41 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:20:41 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:20:43 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:21:07 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:21:07 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 187
[17-Jun-2025 05:21:07 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 286
[17-Jun-2025 05:21:07 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 298
[17-Jun-2025 05:24:10 UTC] PHP Warning:  Undefined property: Better_Payment\Lite\Admin\Elementor\Fundraising_Campaign_Widget::$is_edit_mode in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 183
[17-Jun-2025 05:24:38 UTC] PHP Warning:  Undefined property: Better_Payment\Lite\Admin\Elementor\Fundraising_Campaign_Widget::$is_edit_mode in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 183
[17-Jun-2025 05:24:48 UTC] PHP Warning:  Undefined property: Better_Payment\Lite\Admin\Elementor\Fundraising_Campaign_Widget::$is_edit_mode in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 184
[17-Jun-2025 05:24:48 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 287
[17-Jun-2025 05:24:48 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 299
[17-Jun-2025 05:25:57 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 185
[17-Jun-2025 05:25:57 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 188
[17-Jun-2025 05:25:57 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 287
[17-Jun-2025 05:25:57 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 299
[17-Jun-2025 05:28:39 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 185
[17-Jun-2025 05:28:39 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 188
[17-Jun-2025 05:28:39 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 287
[17-Jun-2025 05:28:39 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 299
[17-Jun-2025 05:28:44 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 185
[17-Jun-2025 05:28:44 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 188
[17-Jun-2025 05:28:44 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 287
[17-Jun-2025 05:28:44 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 299
[17-Jun-2025 05:29:35 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 283
[17-Jun-2025 05:29:35 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 295
[17-Jun-2025 05:29:36 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 283
[17-Jun-2025 05:29:36 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 295
[17-Jun-2025 05:29:51 UTC] PHP Warning:  Undefined variable $comment_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 283
[17-Jun-2025 05:29:51 UTC] PHP Warning:  Undefined variable $update_enable in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 295
[17-Jun-2025 08:17:52 UTC] Automatic updates starting...
[17-Jun-2025 08:17:54 UTC]   Automatic plugin updates starting...
[17-Jun-2025 08:17:54 UTC]   Automatic plugin updates complete.
[17-Jun-2025 08:17:55 UTC]   Automatic theme updates starting...
[17-Jun-2025 08:17:55 UTC]   Automatic theme updates complete.
[17-Jun-2025 08:17:55 UTC] Automatic updates complete.
[17-Jun-2025 09:35:24 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:35:24 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:35:24 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:35:24 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:36:43 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:36:43 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:36:43 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:36:43 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:38:12 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:38:12 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:38:12 UTC] PHP Warning:  Undefined array key "file" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[17-Jun-2025 09:38:12 UTC] PHP Warning:  Undefined array key "line" in /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/dispatchers/Html.php on line 1022
[18-Jun-2025 04:25:50 UTC] Automatic updates starting...
[18-Jun-2025 04:25:51 UTC]   Automatic plugin updates starting...
[18-Jun-2025 04:25:51 UTC]   Automatic plugin updates complete.
[18-Jun-2025 04:25:52 UTC]   Automatic theme updates starting...
[18-Jun-2025 04:25:52 UTC]   Automatic theme updates complete.
[18-Jun-2025 04:25:52 UTC] Automatic updates complete.
[18-Jun-2025 04:39:33 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[18-Jun-2025 04:39:40 UTC] PHP Fatal error:  Maximum execution time of 30 seconds exceeded in /Users/<USER>/Sites/bp/wp-admin/includes/class-wp-filesystem-direct.php on line 82
[18-Jun-2025 05:06:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[18-Jun-2025 07:32:25 UTC] PHP Fatal error:  Maximum execution time of 30 seconds exceeded in /Users/<USER>/Sites/bp/wp-admin/includes/class-wp-filesystem-direct.php on line 78
[18-Jun-2025 08:18:20 UTC] Automatic updates starting...
[18-Jun-2025 08:18:20 UTC]   Automatic plugin updates starting...
[18-Jun-2025 08:18:20 UTC]   Automatic plugin updates complete.
[18-Jun-2025 08:18:20 UTC]   Automatic theme updates starting...
[18-Jun-2025 08:18:20 UTC]   Automatic theme updates complete.
[18-Jun-2025 08:18:20 UTC] Automatic updates complete.
[19-Jun-2025 03:25:02 UTC] Automatic updates starting...
[19-Jun-2025 03:25:03 UTC]   Automatic plugin updates starting...
[19-Jun-2025 03:25:03 UTC]   Automatic plugin updates complete.
[19-Jun-2025 03:25:04 UTC]   Automatic theme updates starting...
[19-Jun-2025 03:25:04 UTC]   Automatic theme updates complete.
[19-Jun-2025 03:25:04 UTC] Automatic updates complete.
[19-Jun-2025 08:28:53 UTC] Automatic updates starting...
[19-Jun-2025 08:28:53 UTC]   Automatic plugin updates starting...
[19-Jun-2025 08:28:53 UTC]   Automatic plugin updates complete.
[19-Jun-2025 08:28:53 UTC]   Automatic theme updates starting...
[19-Jun-2025 08:28:53 UTC]   Automatic theme updates complete.
[19-Jun-2025 08:28:53 UTC] Automatic updates complete.
[19-Jun-2025 08:48:50 UTC] PHP Fatal error:  Uncaught Error: Using $this when not in object context in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php:293
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Better_Payment\Lite\Admin::dispatch_actions()
#1 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#2 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#5 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#6 {main}
  thrown in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php on line 293
[19-Jun-2025 08:49:16 UTC] PHP Fatal error:  Uncaught Error: Using $this when not in object context in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php:293
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Better_Payment\Lite\Admin::dispatch_actions()
#1 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#2 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#5 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#6 {main}
  thrown in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php on line 293
[19-Jun-2025 08:50:51 UTC] PHP Fatal error:  Better_Payment\Lite\Admin cannot use Better_Payment\Lite\Classes\Helper - it is not a trait in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php on line 27
[19-Jun-2025 08:51:17 UTC] PHP Fatal error:  Uncaught Error: Using $this when not in object context in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php:293
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Better_Payment\Lite\Admin::dispatch_actions()
#1 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#2 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#5 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#6 {main}
  thrown in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php on line 293
[19-Jun-2025 08:52:52 UTC] PHP Fatal error:  Cannot use empty array elements in arrays in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php on line 293
[19-Jun-2025 23:11:11 UTC] Automatic updates starting...
[19-Jun-2025 23:11:12 UTC]   Automatic plugin updates starting...
[19-Jun-2025 23:11:12 UTC]   Automatic plugin updates complete.
[19-Jun-2025 23:11:13 UTC]   Automatic theme updates starting...
[19-Jun-2025 23:11:13 UTC]   Automatic theme updates complete.
[19-Jun-2025 23:11:13 UTC] Automatic updates complete.
[20-Jun-2025 11:03:03 UTC] PHP Warning:  wp_version_check(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[20-Jun-2025 11:03:04 UTC] Automatic updates starting...
[20-Jun-2025 11:03:05 UTC]   Automatic plugin updates starting...
[20-Jun-2025 11:03:05 UTC]   Automatic plugin updates complete.
[20-Jun-2025 11:03:06 UTC]   Automatic theme updates starting...
[20-Jun-2025 11:03:06 UTC]   Automatic theme updates complete.
[20-Jun-2025 11:03:06 UTC] Automatic updates complete.
[20-Jun-2025 11:18:29 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[20-Jun-2025 21:57:32 UTC] Automatic updates starting...
[20-Jun-2025 21:57:33 UTC]   Automatic plugin updates starting...
[20-Jun-2025 21:57:33 UTC]   Automatic plugin updates complete.
[20-Jun-2025 21:57:34 UTC]   Automatic theme updates starting...
[20-Jun-2025 21:57:34 UTC]   Automatic theme updates complete.
[20-Jun-2025 21:57:34 UTC] Automatic updates complete.
[20-Jun-2025 22:14:16 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[21-Jun-2025 09:10:27 UTC] Automatic updates starting...
[21-Jun-2025 09:10:28 UTC]   Automatic plugin updates starting...
[21-Jun-2025 09:10:28 UTC]   Automatic plugin updates complete.
[21-Jun-2025 09:10:29 UTC]   Automatic theme updates starting...
[21-Jun-2025 09:10:29 UTC]   Automatic theme updates complete.
[21-Jun-2025 09:10:29 UTC] Automatic updates complete.
[21-Jun-2025 09:18:59 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[22-Jun-2025 00:31:27 UTC] PHP Warning:  wp_version_check(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[22-Jun-2025 00:31:27 UTC] Automatic updates starting...
[22-Jun-2025 00:31:28 UTC]   Automatic plugin updates starting...
[22-Jun-2025 00:31:28 UTC]   Automatic plugin updates complete.
[22-Jun-2025 00:31:29 UTC]   Automatic theme updates starting...
[22-Jun-2025 00:31:29 UTC]   Automatic theme updates complete.
[22-Jun-2025 00:31:29 UTC] Automatic updates complete.
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "better_payment_campaign_layout" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 270
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "better_payment_campaign_currency" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 271
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "better_payment_campaign_header_title_text" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 274
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "better_payment_campaign_header_image_one" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 275
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 275
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "better_payment_campaign_header_short_description" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 276
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 279
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 279
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 280
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 280
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Undefined array key "" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 281
[22-Jun-2025 05:06:47 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/partials/campaign-vars.php on line 281
[22-Jun-2025 05:06:47 UTC] PHP Deprecated:  ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-includes/formatting.php on line 4476
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_facebook_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 224
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 224
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_twitter_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_linkedin_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_instagram_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:23:08 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:23:46 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_twitter_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:23:46 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:23:46 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_linkedin_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:23:46 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:23:46 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_instagram_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:23:46 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:24:55 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_twitter_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:24:55 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:24:55 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_linkedin_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:24:55 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:24:55 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_instagram_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:24:55 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:25:25 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_twitter_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:25:25 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:25:25 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_linkedin_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:25:25 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:25:25 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_instagram_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:25:25 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:25:26 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_twitter_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:25:26 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 229
[22-Jun-2025 06:25:26 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_linkedin_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:25:26 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 234
[22-Jun-2025 06:25:26 UTC] PHP Warning:  Undefined array key "better_payment_campaign_team_member_social_links_instagram_icon" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 06:25:26 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/elementor/fundraising-campaign/layout-1.php on line 239
[22-Jun-2025 08:23:45 UTC] Automatic updates starting...
[22-Jun-2025 08:23:46 UTC]   Automatic plugin updates starting...
[22-Jun-2025 08:23:47 UTC]   Automatic plugin updates complete.
[22-Jun-2025 08:23:48 UTC]   Automatic theme updates starting...
[22-Jun-2025 08:23:48 UTC]   Automatic theme updates complete.
[22-Jun-2025 08:23:48 UTC] Automatic updates complete.
[23-Jun-2025 01:39:12 UTC] PHP Warning:  wp_version_check(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[23-Jun-2025 01:39:13 UTC] Automatic updates starting...
[23-Jun-2025 01:39:14 UTC]   Automatic plugin updates starting...
[23-Jun-2025 01:39:14 UTC]   Automatic plugin updates complete.
[23-Jun-2025 01:39:14 UTC]   Automatic theme updates starting...
[23-Jun-2025 01:39:14 UTC]   Automatic theme updates complete.
[23-Jun-2025 01:39:14 UTC] Automatic updates complete.
[23-Jun-2025 07:19:40 UTC] PHP Parse error:  Unclosed '{' on line 465 in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment-pro/includes/Admin/Elementor/FundraisingCampaign.php on line 467
[23-Jun-2025 07:25:09 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:25:09 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:25:09 UTC] PHP Warning:  Undefined array key "" in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 45
[23-Jun-2025 07:25:09 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 48
[23-Jun-2025 07:25:09 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 49
[23-Jun-2025 07:25:09 UTC] PHP Warning:  Trying to access array offset on value of type null in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 50
[23-Jun-2025 07:25:24 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:25:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:26 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:26 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:35 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:49 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:49 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:50 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:50 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:51 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:51 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:52 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:52 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:59 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:26:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:29 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:30 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:30 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:33 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:34 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:27:34 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:28:03 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:28:03 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:30:26 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:30:26 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:34:21 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:34:21 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:34:41 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:34:41 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:34:47 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:34:47 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:40:35 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:40:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:40:52 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:40:52 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:42:59 UTC] PHP Warning:  Undefined array key 0 in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 07:42:59 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #1 ($search) of type array|string is deprecated in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/page-assets/data-managers/font-icon-svg/font-awesome.php on line 19
[23-Jun-2025 08:17:52 UTC] Automatic updates starting...
[23-Jun-2025 08:17:52 UTC]   Automatic plugin updates starting...
[23-Jun-2025 08:17:52 UTC]   Automatic plugin updates complete.
[23-Jun-2025 08:17:53 UTC]   Automatic theme updates starting...
[23-Jun-2025 08:17:53 UTC]   Automatic theme updates complete.
[23-Jun-2025 08:17:53 UTC] Automatic updates complete.
[23-Jun-2025 10:22:34 UTC] PHP Parse error:  syntax error, unexpected token "<" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment-pro/includes/Admin/Elementor/FundraisingCampaign.php on line 713
[23-Jun-2025 23:28:11 UTC] Automatic updates starting...
[23-Jun-2025 23:28:12 UTC]   Automatic plugin updates starting...
[23-Jun-2025 23:28:12 UTC]   Automatic plugin updates complete.
[23-Jun-2025 23:28:13 UTC]   Automatic theme updates starting...
[23-Jun-2025 23:28:13 UTC]   Automatic theme updates complete.
[23-Jun-2025 23:28:13 UTC] Automatic updates complete.
[24-Jun-2025 04:25:09 UTC] PHP Parse error:  syntax error, unexpected token ";", expecting "]" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment-pro/includes/Admin/Elementor/FundraisingCampaign.php on line 384
[24-Jun-2025 04:59:48 UTC] PHP Parse error:  syntax error, unexpected single-quoted string "{{WRAPPER}} .better-payment .b...", expecting "]" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment-pro/includes/Admin/Elementor/FundraisingCampaign.php on line 468
[24-Jun-2025 05:25:46 UTC] PHP Parse error:  syntax error, unexpected token ";", expecting "]" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment-pro/includes/Admin/Elementor/FundraisingCampaign.php on line 483
[24-Jun-2025 05:33:50 UTC] PHP Parse error:  syntax error, unexpected token "endforeach", expecting "elseif" or "else" or "endif" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment-pro/includes/Admin/Elementor/FundraisingCampaign.php on line 784
[24-Jun-2025 08:17:50 UTC] Automatic updates starting...
[24-Jun-2025 08:17:50 UTC]   Automatic plugin updates starting...
[24-Jun-2025 08:17:50 UTC]   Automatic plugin updates complete.
[24-Jun-2025 08:17:51 UTC]   Automatic theme updates starting...
[24-Jun-2025 08:17:51 UTC]   Automatic theme updates complete.
[24-Jun-2025 08:17:51 UTC] Automatic updates complete.
[24-Jun-2025 22:14:29 UTC] Automatic updates starting...
[24-Jun-2025 22:14:30 UTC]   Automatic plugin updates starting...
[24-Jun-2025 22:14:30 UTC]   Automatic plugin updates complete.
[24-Jun-2025 22:14:31 UTC]   Automatic theme updates starting...
[24-Jun-2025 22:14:31 UTC]   Automatic theme updates complete.
[24-Jun-2025 22:14:31 UTC] Automatic updates complete.
[24-Jun-2025 22:31:44 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[25-Jun-2025 08:18:04 UTC] Automatic updates starting...
[25-Jun-2025 08:18:05 UTC]   Automatic plugin updates starting...
[25-Jun-2025 08:18:05 UTC]   Automatic plugin updates complete.
[25-Jun-2025 08:18:06 UTC]   Automatic theme updates starting...
[25-Jun-2025 08:18:06 UTC]   Automatic theme updates complete.
[25-Jun-2025 08:18:06 UTC] Automatic updates complete.
[25-Jun-2025 12:19:44 UTC] PHP Fatal error:  Maximum execution time of 30 seconds exceeded in /Users/<USER>/Sites/bp/wp-content/plugins/elementor/core/common/modules/ajax/module.php on line 270
[26-Jun-2025 03:31:49 UTC] Automatic updates starting...
[26-Jun-2025 03:31:50 UTC]   Automatic plugin updates starting...
[26-Jun-2025 03:31:50 UTC]   Automatic plugin updates complete.
[26-Jun-2025 03:31:51 UTC]   Automatic theme updates starting...
[26-Jun-2025 03:31:51 UTC]   Automatic theme updates complete.
[26-Jun-2025 03:31:51 UTC] Automatic updates complete.
[26-Jun-2025 07:25:32 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[26-Jun-2025 08:26:30 UTC] Automatic updates starting...
[26-Jun-2025 08:26:30 UTC]   Automatic plugin updates starting...
[26-Jun-2025 08:26:30 UTC]   Automatic plugin updates complete.
[26-Jun-2025 08:26:30 UTC] Automatic updates complete.
[26-Jun-2025 08:48:41 UTC] PHP Warning:  filemtime(): stat failed for /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/assets/css/editor.min.css in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Assets.php on line 139
[26-Jun-2025 08:48:41 UTC] PHP Warning:  filemtime(): stat failed for /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/assets/css/editor.min.css in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Assets.php on line 139
[26-Jun-2025 08:48:48 UTC] PHP Warning:  filemtime(): stat failed for /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/assets/css/editor.min.css in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Assets.php on line 139
[26-Jun-2025 08:48:48 UTC] PHP Warning:  filemtime(): stat failed for /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/assets/css/editor.min.css in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Assets.php on line 139
[26-Jun-2025 09:53:18 UTC] PHP Warning:  Undefined variable $td_campaign_id in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin/views/better-payment-transaction-view.php on line 174
[29-Jun-2025 03:32:30 UTC] Automatic updates starting...
[29-Jun-2025 03:32:31 UTC]   Automatic plugin updates starting...
[29-Jun-2025 03:32:31 UTC]   Automatic plugin updates complete.
[29-Jun-2025 03:32:32 UTC] Automatic updates complete.
[29-Jun-2025 07:52:27 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 07:59:55 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:02:04 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:03:11 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:03:11 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:03:11 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:04:44 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:05:49 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:26:04 UTC] Automatic updates starting...
[29-Jun-2025 08:26:04 UTC]   Automatic plugin updates starting...
[29-Jun-2025 08:26:04 UTC]   Automatic plugin updates complete.
[29-Jun-2025 08:26:06 UTC] Automatic updates complete.
[29-Jun-2025 08:31:54 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:31:54 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:31:56 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:31:57 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:31:58 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:31:58 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:31:58 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:31:58 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:34:14 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:34:14 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:34:14 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:34:14 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:36:08 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:27 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:27 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:27 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:28 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:28 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:28 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:28 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:38:28 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:47:55 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
[29-Jun-2025 08:47:55 UTC] PHP Deprecated:  strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in /Users/<USER>/Sites/bp/wp-admin/admin-header.php on line 41
