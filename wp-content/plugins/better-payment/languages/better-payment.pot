# Copyright (C) 2025 Better Payment
# This file is distributed under the same license as the Better Payment package.
msgid ""
msgstr ""
"Project-Id-Version: Better Payment\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../includes/Admin.php:80, ../includes/Admin/Settings.php:90, ../includes/Admin/Settings.php:91, ../includes/Admin/views/better-payment-settings.php:42
msgid "Settings"
msgstr ""

#: ../includes/Admin.php:83
msgid "Go Pro"
msgstr ""

#: ../includes/Assets.php:158, ../includes/Assets.php:176, ../includes/Admin/Setup_Wizard.php:76
msgid "Are you sure?"
msgstr ""

#: ../includes/Assets.php:159, ../includes/Assets.php:168
msgid "Something went wrong"
msgstr ""

#: ../includes/Assets.php:161
msgid "Redirecting"
msgstr ""

#: ../includes/Assets.php:164
msgid "field is required"
msgstr ""

#: ../includes/Assets.php:165
msgid "Business Email is required"
msgstr ""

#: ../includes/Assets.php:166
msgid "Payment Amount field is required"
msgstr ""

#: ../includes/Assets.php:167
msgid "Minimum amount is 1"
msgstr ""

#: ../includes/Assets.php:177, ../includes/Admin/Setup_Wizard.php:77
msgid "You won't be able to revert this!"
msgstr ""

#: ../includes/Assets.php:178, ../includes/Admin/Setup_Wizard.php:78
msgid "Yes, delete it!"
msgstr ""

#: ../includes/Assets.php:179, ../includes/Admin/Setup_Wizard.php:79
msgid "No, cancel!"
msgstr ""

#: ../includes/Assets.php:182, ../includes/Admin/Setup_Wizard.php:82
msgid "Changes saved successfully!"
msgstr ""

#: ../includes/Assets.php:183, ../includes/Admin/Setup_Wizard.php:83
msgid "Opps! something went wrong!"
msgstr ""

#: ../includes/Assets.php:184, ../includes/Admin/Setup_Wizard.php:84
msgid "No action taken!"
msgstr ""

#: ../includes/Admin/Settings.php:79, ../includes/Admin/Settings.php:80, ../includes/Classes/Actions.php:106, ../includes/Classes/Actions.php:249, ../includes/Classes/Actions.php:255, ../includes/Admin/Elementor/Better_Payment_Widget.php:59, ../includes/Admin/Elementor/Better_Payment_Widget.php:853, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:34, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:46, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:107
msgid "Better Payment"
msgstr ""

#: ../includes/Admin/Settings.php:102, ../includes/Admin/Elementor/User_Dashboard.php:169, ../includes/Admin/Elementor/User_Dashboard.php:239, ../includes/Admin/Elementor/User_Dashboard.php:242
msgid "Transactions"
msgstr ""

#: ../includes/Admin/Settings.php:110
msgid "Analytics"
msgstr ""

#: ../includes/Admin/Settings.php:206, ../includes/Admin/Settings.php:343
msgid "Record not found!"
msgstr ""

#: ../includes/Admin/Settings.php:329, ../includes/Admin/Settings.php:334, ../includes/Admin/Settings.php:362, ../includes/Admin/Settings.php:367, ../includes/Admin/Settings.php:403, ../includes/Admin/Settings.php:408, ../includes/Admin/Settings.php:671, ../includes/Admin/Settings.php:676, ../includes/Classes/Export.php:38, ../includes/Classes/Export.php:43
msgid "Access Denied!"
msgstr ""

#: ../includes/Admin/Settings.php:388
msgid "Something went wrong!"
msgstr ""

#: ../includes/Admin/Settings.php:374
msgid "Deleted Successfully!"
msgstr ""

#: ../includes/Admin/Settings.php:689
msgid "Failed to mark transaction as completed!"
msgstr ""

#: ../includes/Admin/Settings.php:686
msgid "Transaction marked as completed!"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:105, ../includes/Admin/Setup_Wizard.php:106
msgid "Better Payment "
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:138
msgid "Getting Started"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:150
msgid "PayPal Configuration"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:162
msgid "Stripe Configuration"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:172
msgid "Finalize"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:195, ../includes/Admin/views/better-payment-settings.php:76, ../includes/Admin/views/better-payment-settings.php:87, ../includes/Admin/views/better-payment-transaction-list.php:276, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:32, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:44, ../includes/Admin/views/elementor/layouts/layout-1.php:70, ../includes/Admin/views/elementor/layouts/layout-2.php:38
msgid "PayPal"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:196, ../includes/Admin/views/better-payment-settings.php:89
msgid "Enable PayPal if you want to make transaction using PayPal."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:208, ../includes/Admin/views/better-payment-settings.php:77, ../includes/Admin/views/better-payment-settings.php:103, ../includes/Admin/views/better-payment-transaction-list.php:277, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:29, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:45, ../includes/Admin/views/elementor/layouts/layout-1.php:84, ../includes/Admin/views/elementor/layouts/layout-2.php:52
msgid "Stripe"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:209, ../includes/Admin/views/better-payment-settings.php:105
msgid "Enable Stripe if you want to accept payment via Stripe."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:221, ../includes/Admin/views/better-payment-settings.php:152
msgid "Email Notification"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:222, ../includes/Admin/views/better-payment-settings.php:153
msgid "Enable email notification for each transaction. It sends notification to the website admin and customer (who makes the payment). You can modify email settings as per your need."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:234, ../includes/Classes/Handler.php:554, ../includes/Admin/Elementor/Better_Payment_Widget.php:624, ../includes/Admin/Elementor/Better_Payment_Widget.php:2241, ../includes/Admin/Elementor/Better_Payment_Widget.php:2243, ../includes/Admin/Elementor/Better_Payment_Widget.php:2244, ../includes/Admin/views/better-payment-settings.php:165, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:57, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:58
msgid "Currency"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:235, ../includes/Admin/views/better-payment-settings.php:166
msgid "Select default currency for each transaction. You can also overwrite this setting from each widget control on elementor page builder."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:240, ../includes/Admin/views/better-payment-settings.php:171
msgid "Select Currency"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:256, ../includes/Admin/Setup_Wizard.php:283, ../includes/Admin/Elementor/Better_Payment_Widget.php:1592, ../includes/Admin/Elementor/Better_Payment_Widget.php:1696, ../includes/Admin/Elementor/Better_Payment_Widget.php:1762, ../includes/Admin/views/better-payment-settings.php:339, ../includes/Admin/views/better-payment-settings.php:418, ../includes/Admin/views/better-payment-settings.php:487, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:127, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:122, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:121
msgid "Live Mode"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:257, ../includes/Admin/views/better-payment-settings.php:340
msgid "Live mode allows you to process real transactions. It just requires PayPal business email to accept real payments."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:269, ../includes/Admin/Elementor/Better_Payment_Widget.php:1562, ../includes/Admin/views/better-payment-settings.php:352, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:97
msgid "Business Email"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:270, ../includes/Admin/views/better-payment-settings.php:353
msgid "Your PayPal account email address to accept payment via PayPal."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:284, ../includes/Admin/views/better-payment-settings.php:419
msgid "Live mode allows you to process real transactions. It just requires live Stripe keys (public and secret keys) to accept real payments."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:299, ../includes/Admin/Elementor/Better_Payment_Widget.php:1657, ../includes/Admin/views/better-payment-settings.php:434, ../includes/Admin/views/better-payment-settings.php:503
msgid "Live Public Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:300, ../includes/Admin/views/better-payment-settings.php:436
msgid "Stripe live public key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:308, ../includes/Admin/Elementor/Better_Payment_Widget.php:1676, ../includes/Admin/views/better-payment-settings.php:446, ../includes/Admin/views/better-payment-settings.php:515
msgid "Live Secret Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:309, ../includes/Admin/views/better-payment-settings.php:448
msgid "Stripe live secret key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:317, ../includes/Admin/Elementor/Better_Payment_Widget.php:1618, ../includes/Admin/views/better-payment-settings.php:458, ../includes/Admin/views/better-payment-settings.php:527
msgid "Test Public Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:318, ../includes/Admin/views/better-payment-settings.php:460
msgid "Stripe test public key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:326, ../includes/Admin/Elementor/Better_Payment_Widget.php:1637, ../includes/Admin/views/better-payment-settings.php:470, ../includes/Admin/views/better-payment-settings.php:539
msgid "Test Secret Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:327, ../includes/Admin/views/better-payment-settings.php:472
msgid "Stripe test secret key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:348
msgid "Great Job! Your Configuration is Complete "
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:350
msgid "Share non-sensitive diagnostic data and plugin usage information."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:352
msgid "What do we collect? We collect non-sensitive diagnostic data and plugin usage information. Your site URL, WordPress & PHP version, plugins & themes and email address to send you the discount coupon. This data lets us make sure this plugin always stays compatible with the most popular plugins and themes. No spam, we promise."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:368
msgid "< Previous"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:369
msgid "Next >"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:370
msgid "Finish"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:488
msgid "Quick Setup Wizard - Better Payment"
msgstr ""

#: ../includes/Classes/Actions.php:168, ../includes/Classes/Actions.php:390
msgid "Page ID is missing"
msgstr ""

#: ../includes/Classes/Actions.php:175, ../includes/Classes/Actions.php:394
msgid "Widget ID is missing"
msgstr ""

#: ../includes/Classes/Actions.php:188, ../includes/Classes/Actions.php:407
msgid "Setting Data is missing"
msgstr ""

#: ../includes/Classes/Actions.php:208
msgid "Stripe Key missing"
msgstr ""

#: ../includes/Classes/Actions.php:411
msgid "Paystack Key missing"
msgstr ""

#: ../includes/Classes/Handler.php:231, ../includes/Classes/Handler.php:383, ../includes/Classes/Handler.php:472
msgid "USD"
msgstr ""

#: ../includes/Classes/Handler.php:267
msgid "Payment under processing!"
msgstr ""

#: ../includes/Classes/Handler.php:322, ../includes/Classes/Handler.php:343
msgid "There was a problem connecting to the Stripe API endpoint."
msgstr ""

#: ../includes/Classes/Handler.php:512, ../includes/Admin/Elementor/Better_Payment_Widget.php:2172, ../includes/Admin/Elementor/Better_Payment_Widget.php:2173, ../includes/Admin/Elementor/Better_Payment_Widget.php:2174
msgid "You paid"
msgstr ""

#: ../includes/Classes/Handler.php:512
msgid " to "
msgstr ""

#: ../includes/Classes/Handler.php:522
msgid "Payment Confirmation email will be sent to "
msgstr ""

#: ../includes/Classes/Handler.php:545, ../includes/Admin/views/template-email-notification.php:173
msgid "Thank You!"
msgstr ""

#: ../includes/Classes/Handler.php:548, ../includes/Admin/Elementor/Better_Payment_Widget.php:2192, ../includes/Admin/Elementor/Better_Payment_Widget.php:2193, ../includes/Admin/Elementor/User_Dashboard.php:1907, ../includes/Admin/Elementor/User_Dashboard.php:2030, ../includes/Admin/Elementor/User_Dashboard.php:2033, ../includes/Admin/views/template-transaction-list.php:44
msgid "Transaction ID"
msgstr ""

#: ../includes/Classes/Handler.php:551, ../includes/Admin/Elementor/Better_Payment_Widget.php:1192, ../includes/Admin/Elementor/Better_Payment_Widget.php:2224, ../includes/Admin/Elementor/Better_Payment_Widget.php:2226, ../includes/Admin/Elementor/Better_Payment_Widget.php:2227, ../includes/Admin/Elementor/User_Dashboard.php:1883, ../includes/Admin/Elementor/User_Dashboard.php:2004, ../includes/Admin/Elementor/User_Dashboard.php:2007, ../includes/Admin/views/better-payment-transaction-list.php:229, ../includes/Admin/views/template-email-notification.php:328, ../includes/Admin/views/template-transaction-list.php:38
msgid "Amount"
msgstr ""

#: ../includes/Classes/Handler.php:555, ../includes/Admin/Elementor/Better_Payment_Widget.php:2258, ../includes/Admin/Elementor/Better_Payment_Widget.php:2260, ../includes/Admin/Elementor/Better_Payment_Widget.php:2261, ../includes/Admin/Elementor/Better_Payment_Widget.php:3258
msgid "Payment Method"
msgstr ""

#: ../includes/Classes/Handler.php:558, ../includes/Admin/Elementor/Better_Payment_Widget.php:116, ../includes/Admin/Elementor/Better_Payment_Widget.php:2275, ../includes/Admin/Elementor/Better_Payment_Widget.php:2277, ../includes/Admin/Elementor/Better_Payment_Widget.php:2278, ../includes/Admin/Elementor/User_Dashboard.php:1895, ../includes/Admin/Elementor/User_Dashboard.php:2017, ../includes/Admin/Elementor/User_Dashboard.php:2020, ../includes/Admin/views/template-transaction-list.php:41
msgid "Payment Type"
msgstr ""

#: ../includes/Classes/Handler.php:568
msgid "Split Payment"
msgstr ""

#: ../includes/Classes/Handler.php:566
msgid "Recurring Payment"
msgstr ""

#: ../includes/Classes/Handler.php:564
msgid "One Time Payment"
msgstr ""

#: ../includes/Classes/Handler.php:573, ../includes/Admin/Elementor/Better_Payment_Widget.php:2292, ../includes/Admin/Elementor/Better_Payment_Widget.php:2294, ../includes/Admin/Elementor/Better_Payment_Widget.php:2295
msgid "Merchant Details"
msgstr ""

#: ../includes/Classes/Handler.php:579, ../includes/Admin/Elementor/Better_Payment_Widget.php:2309, ../includes/Admin/Elementor/Better_Payment_Widget.php:2311, ../includes/Admin/Elementor/Better_Payment_Widget.php:2312
msgid "Paid Amount"
msgstr ""

#: ../includes/Classes/Handler.php:582, ../includes/Admin/Elementor/Better_Payment_Widget.php:2326, ../includes/Admin/Elementor/Better_Payment_Widget.php:2328, ../includes/Admin/Elementor/Better_Payment_Widget.php:2329
msgid "Purchase Details"
msgstr ""

#: ../includes/Classes/Handler.php:585, ../includes/Admin/Elementor/Better_Payment_Widget.php:2343, ../includes/Admin/Elementor/Better_Payment_Widget.php:2345, ../includes/Admin/Elementor/Better_Payment_Widget.php:2346
msgid "Print"
msgstr ""

#: ../includes/Classes/Handler.php:588, ../includes/Admin/Elementor/Better_Payment_Widget.php:2360, ../includes/Admin/Elementor/Better_Payment_Widget.php:2362, ../includes/Admin/Elementor/Better_Payment_Widget.php:2363
msgid "View Details"
msgstr ""

#: ../includes/Classes/Handler.php:770, ../includes/Admin/Elementor/Better_Payment_Widget.php:2432
msgid "Payment Failed"
msgstr ""

#: ../includes/Classes/Handler.php:814, ../includes/Admin/Elementor/Better_Payment_Widget.php:1817, ../includes/Admin/Elementor/Better_Payment_Widget.php:1984
msgid "Better Payment transaction on %s"
msgstr ""

#: ../includes/Classes/Handler.php:954, ../includes/Classes/Handler.php:958
msgid "New better payment transaction! "
msgstr ""

#: ../includes/Classes/Handler.php:1088
msgid "Field"
msgstr ""

#: ../includes/Classes/Handler.php:1089
msgid "Entry"
msgstr ""

#: ../includes/Classes/Handler.php:1142, ../includes/Admin/views/template-email-notification.php:252
msgid "Paid"
msgstr ""

#: ../includes/Classes/Handler.php:1145
msgid "Product Price: "
msgstr ""

#: ../includes/Classes/Helper.php:54
msgid "%1$sBetter Payment%2$s requires %1$sElementor%2$s plugin to be installed and activated. Please install Elementor to continue."
msgstr ""

#: ../includes/Classes/Helper.php:55
msgid "Install Elementor"
msgstr ""

#: ../includes/Classes/Helper.php:48
msgid "%1$sBetter Payment%2$s requires %1$sElementor%2$s plugin to be active. Please activate Elementor to continue."
msgstr ""

#: ../includes/Classes/Helper.php:50
msgid "Activate Elementor"
msgstr ""

#: ../includes/Classes/Import.php:38, ../includes/Classes/Import.php:137
msgid "Invalid File!"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:416
msgid "We can't detect any plugin information. This is most probably because you have not included the code in the plugin main file."
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:778
msgid "Sorry to see you go"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:779
msgid "Before you deactivate the plugin, would you quickly give us your reason for doing so?"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:782
msgid "I no longer need the plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:784
msgid "I found a better plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:785
msgid "Please share which plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:787
msgid "I couldn't get the plugin to work"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:788
msgid "It's a temporary deactivation"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:790
msgid "Other"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:791
msgid "Please share the reason"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:834
msgid "Submitting form"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:892
msgid "Submit and Deactivate"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:892
msgid "Just Deactivate"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:97
msgid "Payment Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:106
msgid "Form Layout"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:117
msgid "Recurring and Split Payment is available for Stripe only at the moment!"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:134
msgid "Default Price ID"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:137
msgid "<p>Create a product from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the (default) price id.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:158
msgid "Installment Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:171, ../includes/Admin/Elementor/Better_Payment_Widget.php:265
msgid "Price ID"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:184
msgid "Iterations"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:196
msgid "<p>Now add more prices to the product from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the price id for each installment.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:213
msgid "Installments"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:252
msgid "Interval Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:278
msgid "Intervals"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:303
msgid "Webhook Secret"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:306
msgid "<p>Create a webhook endpoint from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the webhook secret.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:326
msgid "<p><a href=\"%1$s\" target=\"_blank\">Your webhook endpoint url »</a><br>%2$s</p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:344
msgid "Payment Source"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:359
msgid "<a href=\"%1$s\" target=\"_blank\"><strong>WooCommerce</strong></a> is not installed/activated on your site. Please install and activate <a href=\"%1$s\" target=\"_blank\"><strong>WooCommerce</strong></a> first."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:394
msgid "Product"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:403
msgid "Choose a Product"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:404
msgid "Enter Product IDs separated by a comma"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:410
msgid "Search By"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:419
msgid "Select Products"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:433
msgid "Enable PayPal"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:435, ../includes/Admin/Elementor/Better_Payment_Widget.php:461, ../includes/Admin/Elementor/Better_Payment_Widget.php:538, ../includes/Admin/Elementor/Better_Payment_Widget.php:581, ../includes/Admin/Elementor/Better_Payment_Widget.php:593, ../includes/Admin/Elementor/Better_Payment_Widget.php:613, ../includes/Admin/Elementor/Better_Payment_Widget.php:948, ../includes/Admin/Elementor/Better_Payment_Widget.php:975, ../includes/Admin/Elementor/Better_Payment_Widget.php:1024, ../includes/Admin/Elementor/Better_Payment_Widget.php:1057, ../includes/Admin/Elementor/Better_Payment_Widget.php:1071, ../includes/Admin/Elementor/Better_Payment_Widget.php:1309, ../includes/Admin/Elementor/Better_Payment_Widget.php:1325, ../includes/Admin/Elementor/Better_Payment_Widget.php:1594, ../includes/Admin/Elementor/Better_Payment_Widget.php:1698, ../includes/Admin/Elementor/Better_Payment_Widget.php:1764, ../includes/Admin/Elementor/Better_Payment_Widget.php:1844, ../includes/Admin/Elementor/Better_Payment_Widget.php:1856, ../includes/Admin/Elementor/Better_Payment_Widget.php:1868, ../includes/Admin/Elementor/Better_Payment_Widget.php:1880, ../includes/Admin/Elementor/Better_Payment_Widget.php:1892, ../includes/Admin/Elementor/Better_Payment_Widget.php:1904, ../includes/Admin/Elementor/Better_Payment_Widget.php:2012, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:80, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:119, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:176, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:283, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:129, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:124, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:123
msgid "Yes"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:436, ../includes/Admin/Elementor/Better_Payment_Widget.php:462, ../includes/Admin/Elementor/Better_Payment_Widget.php:539, ../includes/Admin/Elementor/Better_Payment_Widget.php:582, ../includes/Admin/Elementor/Better_Payment_Widget.php:594, ../includes/Admin/Elementor/Better_Payment_Widget.php:614, ../includes/Admin/Elementor/Better_Payment_Widget.php:949, ../includes/Admin/Elementor/Better_Payment_Widget.php:976, ../includes/Admin/Elementor/Better_Payment_Widget.php:1025, ../includes/Admin/Elementor/Better_Payment_Widget.php:1058, ../includes/Admin/Elementor/Better_Payment_Widget.php:1072, ../includes/Admin/Elementor/Better_Payment_Widget.php:1310, ../includes/Admin/Elementor/Better_Payment_Widget.php:1326, ../includes/Admin/Elementor/Better_Payment_Widget.php:1595, ../includes/Admin/Elementor/Better_Payment_Widget.php:1699, ../includes/Admin/Elementor/Better_Payment_Widget.php:1765, ../includes/Admin/Elementor/Better_Payment_Widget.php:1845, ../includes/Admin/Elementor/Better_Payment_Widget.php:1857, ../includes/Admin/Elementor/Better_Payment_Widget.php:1869, ../includes/Admin/Elementor/Better_Payment_Widget.php:1881, ../includes/Admin/Elementor/Better_Payment_Widget.php:1893, ../includes/Admin/Elementor/Better_Payment_Widget.php:1905, ../includes/Admin/Elementor/Better_Payment_Widget.php:2013, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:81, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:120, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:177, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:284, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:130, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:125, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:124
msgid "No"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:448
msgid "Whoops! It seems like you haven't configured <b>PayPal (Business Email) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:459
msgid "Enable Stripe"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:470, ../includes/Admin/Elementor/Better_Payment_Widget.php:503
msgid "Whoops! It seems like you haven't configured <b>Stripe (Public and Secret Key) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:536
msgid "Enable Paystack"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:550
msgid "Whoops! It seems like you haven't configured <b>Paystack (Public and Secret Key) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:579
msgid "Enable Email Notification"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:591
msgid "Show Sidebar"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:611
msgid "Use WooCommerce Currency?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:723
msgid "WooCommerce Currency"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:741
msgid "Supported by %1$sStripe%2$s (and/or %1$sPaystack%2$s) only"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:762, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:79, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:68
msgid "Currency Alignment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:766, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:83, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:72
msgid "Left"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:770, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:87, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:76
msgid "Right"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:824
msgid "Form Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:848, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:102
msgid "Form Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:867, ../includes/Admin/Elementor/Better_Payment_Widget.php:869, ../includes/Admin/Elementor/Better_Payment_Widget.php:882
msgid "Field Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:880
msgid "Placeholder Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:893, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:248
msgid "Field Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:897
msgid "Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:898, ../includes/Admin/Elementor/Better_Payment_Widget.php:925, ../includes/Admin/Elementor/Better_Payment_Widget.php:1183, ../includes/Admin/views/better-payment-settings.php:67
msgid "Email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:899
msgid "Number"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:918
msgid "Primary Field Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:919
msgid "If this is a primary field (first name, last name, email etc), then please select one."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:923, ../includes/Admin/Elementor/Better_Payment_Widget.php:1119, ../includes/Admin/Elementor/Better_Payment_Widget.php:1120
msgid "First Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:924, ../includes/Admin/Elementor/Better_Payment_Widget.php:1128, ../includes/Admin/Elementor/Better_Payment_Widget.php:1129
msgid "Last Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:926, ../includes/Admin/Elementor/Better_Payment_Widget.php:1146, ../includes/Admin/Elementor/Better_Payment_Widget.php:1147, ../includes/Admin/Elementor/Better_Payment_Widget.php:1298, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:31, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:93
msgid "Payment Amount"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:927
msgid "None"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:936, ../includes/Admin/Elementor/Better_Payment_Widget.php:2154, ../includes/Admin/Elementor/Better_Payment_Widget.php:2421, ../includes/Admin/Elementor/Better_Payment_Widget.php:2862, ../includes/Admin/Elementor/User_Dashboard.php:643
msgid "Icon"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:937
msgid "Select an icon for this field (not applicable for primary field - Payment Amount and layout 4, 5, 6)."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:946, ../includes/Admin/Elementor/User_Dashboard.php:117, ../includes/Admin/Elementor/User_Dashboard.php:129, ../includes/Admin/Elementor/User_Dashboard.php:144, ../includes/Admin/Elementor/User_Dashboard.php:159, ../includes/Admin/Elementor/User_Dashboard.php:171, ../includes/Admin/Elementor/User_Dashboard.php:183, ../includes/Admin/Elementor/User_Dashboard.php:195, ../includes/Admin/Elementor/User_Dashboard.php:1662, ../includes/Admin/Elementor/User_Dashboard.php:1674, ../includes/Admin/Elementor/User_Dashboard.php:1686, ../includes/Admin/Elementor/User_Dashboard.php:1698, ../includes/Admin/Elementor/User_Dashboard.php:1710, ../includes/Admin/Elementor/User_Dashboard.php:1861, ../includes/Admin/Elementor/User_Dashboard.php:1873, ../includes/Admin/Elementor/User_Dashboard.php:1885, ../includes/Admin/Elementor/User_Dashboard.php:1897, ../includes/Admin/Elementor/User_Dashboard.php:1909, ../includes/Admin/Elementor/User_Dashboard.php:1921, ../includes/Admin/Elementor/User_Dashboard.php:1933, ../includes/Admin/Elementor/User_Dashboard.php:1945
msgid "Show"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:961
msgid "Field is hidden if payment source is WooCommerce or payment type is recurring/split payment or field dynamic value is enabled."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:973
msgid "Required"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:988, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:289
msgid "Min. Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:999, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:300
msgid "Max. Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1010, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:311
msgid "Default Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1021
msgid "Dynamic Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1023
msgid "It will override default value!"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1038
msgid "<p class=\"better-payment-dynamic-value-info\" style=\"word-break: break-word;\"><a href=\"%1$s\" target=\"_blank\">Sample url »</a><br>%1$s</p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1055, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:322
msgid "Readonly"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1069
msgid "Display Inline?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1089
msgid "Column Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1114
msgid "Set the width of the column. Use less than 50% to make fields inline"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1137, ../includes/Admin/Elementor/Better_Payment_Widget.php:1138, ../includes/Admin/Elementor/User_Dashboard.php:1871, ../includes/Admin/Elementor/User_Dashboard.php:1991, ../includes/Admin/Elementor/User_Dashboard.php:1994, ../includes/Admin/views/better-payment-transaction-list.php:228, ../includes/Admin/views/template-transaction-list.php:35
msgid "Email Address"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1155, ../includes/Admin/Elementor/Better_Payment_Widget.php:1156
msgid "First name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1169, ../includes/Admin/Elementor/Better_Payment_Widget.php:1170
msgid "Last name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1184
msgid "Enter your email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1193, ../includes/Admin/Elementor/Better_Payment_Widget.php:1202
msgid "Other amount"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1201
msgid "Amount to pay"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1239, ../includes/Admin/Elementor/Better_Payment_Widget.php:1253, ../includes/Admin/Elementor/Better_Payment_Widget.php:1267, ../includes/Admin/Elementor/Better_Payment_Widget.php:1281
msgid "Form Fields"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1307, ../includes/Admin/Elementor/Better_Payment_Widget.php:1323, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:117
msgid "Show Amount List"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1339, ../includes/Admin/Elementor/Better_Payment_Widget.php:1369, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:253, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:146
msgid "Amount List"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1403, ../includes/Admin/Elementor/Better_Payment_Widget.php:1414
msgid "Transaction Details"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1412
msgid "Heading Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1425
msgid "Sub Heading Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1427
msgid "Total payment of your product in the following:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1438
msgid "Product Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1440
msgid "Title:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1454, ../includes/Admin/Elementor/Better_Payment_Widget.php:2710
msgid "Amount Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1456, ../includes/Admin/views/better-payment-transaction-view.php:130
msgid "Amount:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1472
msgid "Form Custom Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1481
msgid "Form Title"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1496
msgid "Form Sub-Title"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1511
msgid "PayPal Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1523
msgid "Stripe Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1535
msgid "Paystack Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1551
msgid "PayPal Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1578, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:113
msgid "Button Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1608
msgid "Stripe Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1712
msgid "Paystack Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1726, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:89, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:88
msgid "Public Key"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1745, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:105, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:104
msgid "Secret Key"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1778
msgid "Email Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1788
msgid "Choose Logo"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1801
msgid "Admin"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1805, ../includes/Admin/views/better-payment-settings.php:185, ../includes/Admin/views/better-payment-settings.php:262, ../includes/Admin/views/template-email-notification.php:270
msgid "To"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1810
msgid "Email address to notify site admin after each successful transaction"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1820, ../includes/Admin/Elementor/Better_Payment_Widget.php:1987, ../includes/Admin/views/better-payment-settings.php:192, ../includes/Admin/views/better-payment-settings.php:268
msgid "Subject"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1832, ../includes/Admin/Elementor/Better_Payment_Widget.php:1999, ../includes/Admin/views/better-payment-settings.php:199, ../includes/Admin/views/better-payment-settings.php:275
msgid "Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1842
msgid "Show Greeting Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1854
msgid "Show Header Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1866
msgid "Show From Section"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1878
msgid "Show To Section"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1890
msgid "Show Transaction Summary"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1902
msgid "Show Footer Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1915, ../includes/Admin/Elementor/Better_Payment_Widget.php:2057
msgid "From email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1926, ../includes/Admin/Elementor/Better_Payment_Widget.php:2068
msgid "From name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1936, ../includes/Admin/Elementor/Better_Payment_Widget.php:2078, ../includes/Admin/views/better-payment-settings.php:223, ../includes/Admin/views/better-payment-settings.php:299
msgid "Reply-To"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1947, ../includes/Admin/Elementor/Better_Payment_Widget.php:2089, ../includes/Admin/views/better-payment-settings.php:230, ../includes/Admin/views/better-payment-settings.php:306
msgid "Cc"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1957, ../includes/Admin/Elementor/Better_Payment_Widget.php:2099, ../includes/Admin/views/better-payment-settings.php:237, ../includes/Admin/views/better-payment-settings.php:313
msgid "Bcc"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1967, ../includes/Admin/Elementor/Better_Payment_Widget.php:2109
msgid "Send as"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1972, ../includes/Admin/Elementor/Better_Payment_Widget.php:2114
msgid "HTML"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1973, ../includes/Admin/Elementor/Better_Payment_Widget.php:2115, ../includes/Admin/views/better-payment-settings.php:249, ../includes/Admin/views/better-payment-settings.php:325
msgid "Plain"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1981
msgid "Customer"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2009
msgid "PDF Attachment?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2021
msgid "Attachment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2022
msgid "Allowed file types: jpg, jpeg, png"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2037
msgid "PDF"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2038
msgid "Allowed file types: pdf"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2130
msgid "Success Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2163, ../includes/Admin/Elementor/User_Dashboard.php:213
msgid "Content"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2170
msgid "Heading"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2172, ../includes/Admin/Elementor/Better_Payment_Widget.php:2173, ../includes/Admin/Elementor/Better_Payment_Widget.php:2174
msgid "to"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2174
msgid "Use shortcode like"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2174
msgid "to customize your message."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2174
msgid "eg:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2174
msgid "for your order."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2179
msgid "Sub Heading"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2181, ../includes/Admin/Elementor/Better_Payment_Widget.php:2182, ../includes/Admin/Elementor/Better_Payment_Widget.php:2183
msgid "Payment confirmation email will be sent to"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2190
msgid "Transaction"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2207
msgid "Thank You"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2209, ../includes/Admin/Elementor/Better_Payment_Widget.php:2210
msgid "Thank you for your payment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2376
msgid "User Dashboard URL"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2378, ../includes/Admin/Elementor/Better_Payment_Widget.php:2386, ../includes/Admin/Elementor/Better_Payment_Widget.php:2447
msgid "eg. https://example.com/custom-page/"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2379
msgid "Please enter the page url where <strong>User Dashboard</strong> widget is used."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2384, ../includes/Admin/Elementor/Better_Payment_Widget.php:2445
msgid "Custom Redirect URL"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2387, ../includes/Admin/Elementor/Better_Payment_Widget.php:2448
msgid "Please note that only your current domain is allowed here to keep your site secure."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2397
msgid "Error Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2430
msgid "Heading Message Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2468
msgid "Form Sidebar Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2485, ../includes/Admin/Elementor/Better_Payment_Widget.php:2604, ../includes/Admin/Elementor/Better_Payment_Widget.php:2678, ../includes/Admin/Elementor/Better_Payment_Widget.php:2752, ../includes/Admin/Elementor/Better_Payment_Widget.php:2828, ../includes/Admin/Elementor/Better_Payment_Widget.php:2914, ../includes/Admin/Elementor/Better_Payment_Widget.php:2965, ../includes/Admin/Elementor/User_Dashboard.php:308, ../includes/Admin/Elementor/User_Dashboard.php:446, ../includes/Admin/Elementor/User_Dashboard.php:694, ../includes/Admin/Elementor/User_Dashboard.php:857, ../includes/Admin/Elementor/User_Dashboard.php:978, ../includes/Admin/Elementor/User_Dashboard.php:1131
msgid "Margin"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2501, ../includes/Admin/Elementor/Better_Payment_Widget.php:2621, ../includes/Admin/Elementor/Better_Payment_Widget.php:2694, ../includes/Admin/Elementor/Better_Payment_Widget.php:2769, ../includes/Admin/Elementor/Better_Payment_Widget.php:2845, ../includes/Admin/Elementor/Better_Payment_Widget.php:2929, ../includes/Admin/Elementor/Better_Payment_Widget.php:2980, ../includes/Admin/Elementor/Better_Payment_Widget.php:3111, ../includes/Admin/Elementor/Better_Payment_Widget.php:3731, ../includes/Admin/Elementor/User_Dashboard.php:320, ../includes/Admin/Elementor/User_Dashboard.php:458, ../includes/Admin/Elementor/User_Dashboard.php:706, ../includes/Admin/Elementor/User_Dashboard.php:869, ../includes/Admin/Elementor/User_Dashboard.php:990, ../includes/Admin/Elementor/User_Dashboard.php:1143
msgid "Padding"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2516, ../includes/Admin/Elementor/Better_Payment_Widget.php:2995, ../includes/Admin/Elementor/Better_Payment_Widget.php:3225, ../includes/Admin/Elementor/Better_Payment_Widget.php:3600, ../includes/Admin/Elementor/Better_Payment_Widget.php:3716, ../includes/Admin/Elementor/User_Dashboard.php:333, ../includes/Admin/Elementor/User_Dashboard.php:482, ../includes/Admin/Elementor/User_Dashboard.php:719, ../includes/Admin/Elementor/User_Dashboard.php:882, ../includes/Admin/Elementor/User_Dashboard.php:1003, ../includes/Admin/Elementor/User_Dashboard.php:1156, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:266, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:471
msgid "Border Radius"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2552
msgid "Sidebar Text Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2563
msgid "Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2574, ../includes/Admin/Elementor/Better_Payment_Widget.php:2650, ../includes/Admin/Elementor/Better_Payment_Widget.php:2722, ../includes/Admin/Elementor/Better_Payment_Widget.php:2798, ../includes/Admin/Elementor/Better_Payment_Widget.php:2874, ../includes/Admin/Elementor/Better_Payment_Widget.php:3334, ../includes/Admin/Elementor/User_Dashboard.php:671, ../includes/Admin/Elementor/User_Dashboard.php:749, ../includes/Admin/Elementor/User_Dashboard.php:793, ../includes/Admin/Elementor/User_Dashboard.php:1033, ../includes/Admin/Elementor/User_Dashboard.php:1078, ../includes/Admin/Elementor/User_Dashboard.php:1186, ../includes/Admin/Elementor/User_Dashboard.php:1233, ../includes/Admin/Elementor/User_Dashboard.php:1299, ../includes/Admin/Elementor/User_Dashboard.php:1327, ../includes/Admin/Elementor/User_Dashboard.php:1367, ../includes/Admin/Elementor/User_Dashboard.php:1395, ../includes/Admin/Elementor/User_Dashboard.php:1435, ../includes/Admin/Elementor/User_Dashboard.php:1463
msgid "Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2592, ../includes/Admin/Elementor/Better_Payment_Widget.php:2667, ../includes/Admin/Elementor/Better_Payment_Widget.php:2741, ../includes/Admin/Elementor/Better_Payment_Widget.php:2817, ../includes/Admin/Elementor/Better_Payment_Widget.php:3242, ../includes/Admin/Elementor/Better_Payment_Widget.php:3616, ../includes/Admin/Elementor/Better_Payment_Widget.php:3769, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:487
msgid "Typography"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2638
msgid "Sub-Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2786
msgid "Amount Summary"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2889
msgid "Font Size"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2948
msgid "Form Container Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3031
msgid "Form Fields Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3039, ../includes/Admin/Elementor/Better_Payment_Widget.php:3515, ../includes/Admin/Elementor/Better_Payment_Widget.php:3554, ../includes/Admin/Elementor/Better_Payment_Widget.php:3676, ../includes/Admin/Elementor/Better_Payment_Widget.php:3795, ../includes/Admin/Elementor/User_Dashboard.php:358, ../includes/Admin/Elementor/User_Dashboard.php:391, ../includes/Admin/Elementor/User_Dashboard.php:471, ../includes/Admin/Elementor/User_Dashboard.php:760, ../includes/Admin/Elementor/User_Dashboard.php:804, ../includes/Admin/Elementor/User_Dashboard.php:904, ../includes/Admin/Elementor/User_Dashboard.php:937, ../includes/Admin/Elementor/User_Dashboard.php:1045, ../includes/Admin/Elementor/User_Dashboard.php:1090, ../includes/Admin/Elementor/User_Dashboard.php:1200, ../includes/Admin/Elementor/User_Dashboard.php:1245, ../includes/Admin/Elementor/User_Dashboard.php:1310, ../includes/Admin/Elementor/User_Dashboard.php:1338, ../includes/Admin/Elementor/User_Dashboard.php:1378, ../includes/Admin/Elementor/User_Dashboard.php:1406, ../includes/Admin/Elementor/User_Dashboard.php:1446, ../includes/Admin/Elementor/User_Dashboard.php:1474, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:221, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:380, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:422
msgid "Background Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3054, ../includes/Admin/Elementor/Better_Payment_Widget.php:3530, ../includes/Admin/Elementor/Better_Payment_Widget.php:3569, ../includes/Admin/Elementor/Better_Payment_Widget.php:3691, ../includes/Admin/Elementor/Better_Payment_Widget.php:3811, ../includes/Admin/Elementor/User_Dashboard.php:556, ../includes/Admin/Elementor/User_Dashboard.php:573, ../includes/Admin/Elementor/User_Dashboard.php:611, ../includes/Admin/Elementor/User_Dashboard.php:628, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:190, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:395, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:437
msgid "Text Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3070
msgid "Placeholder Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3085, ../includes/Admin/Elementor/Better_Payment_Widget.php:3480, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:342
msgid "Spacing"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3126
msgid "Text Indent"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3153, ../includes/Admin/Elementor/Better_Payment_Widget.php:3436, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:298
msgid "Input Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3163
msgid "Set width for all input fields. Not applicable if the field is set to display inline (<b>Content => Form Settings => Form Fields (Repeater) => Display Inline?</b>)"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3184, ../includes/Admin/Elementor/Better_Payment_Widget.php:3458, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:320
msgid "Input Height"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3215, ../includes/Admin/Elementor/Better_Payment_Widget.php:3283, ../includes/Admin/Elementor/Better_Payment_Widget.php:3307, ../includes/Admin/Elementor/Better_Payment_Widget.php:3589, ../includes/Admin/Elementor/Better_Payment_Widget.php:3707, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:457
msgid "Border"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3272
msgid "Active"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3296
msgid "Inactive"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3322
msgid "Input Icon"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3353, ../includes/Admin/Elementor/User_Dashboard.php:523, ../includes/Admin/Elementor/User_Dashboard.php:652
msgid "Size"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3377, ../includes/Admin/Elementor/Better_Payment_Widget.php:3645
msgid "Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3400
msgid "Height"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3428
msgid "Amount Fields Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3508, ../includes/Admin/Elementor/Better_Payment_Widget.php:3669, ../includes/Admin/Elementor/User_Dashboard.php:352, ../includes/Admin/Elementor/User_Dashboard.php:550, ../includes/Admin/Elementor/User_Dashboard.php:605, ../includes/Admin/Elementor/User_Dashboard.php:743, ../includes/Admin/Elementor/User_Dashboard.php:898, ../includes/Admin/Elementor/User_Dashboard.php:1027, ../includes/Admin/Elementor/User_Dashboard.php:1180, ../includes/Admin/Elementor/User_Dashboard.php:1293, ../includes/Admin/Elementor/User_Dashboard.php:1361, ../includes/Admin/Elementor/User_Dashboard.php:1429, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:370
msgid "Normal"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3547, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:412
msgid "Selected"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3637
msgid "Form Button Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3746
msgid "Margin Top"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3788, ../includes/Admin/Elementor/User_Dashboard.php:385, ../includes/Admin/Elementor/User_Dashboard.php:567, ../includes/Admin/Elementor/User_Dashboard.php:622, ../includes/Admin/Elementor/User_Dashboard.php:787, ../includes/Admin/Elementor/User_Dashboard.php:931, ../includes/Admin/Elementor/User_Dashboard.php:1072, ../includes/Admin/Elementor/User_Dashboard.php:1227, ../includes/Admin/Elementor/User_Dashboard.php:1321, ../includes/Admin/Elementor/User_Dashboard.php:1389, ../includes/Admin/Elementor/User_Dashboard.php:1457
msgid "Hover"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3827, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:236
msgid "Border Color"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:58, ../includes/Admin/views/better-payment-settings.php:136
msgid "User Dashboard"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:98, ../includes/Admin/Elementor/User_Dashboard.php:105
msgid "Layout"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:115, ../includes/Admin/Elementor/User_Dashboard.php:426
msgid "Sidebar"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:118, ../includes/Admin/Elementor/User_Dashboard.php:130, ../includes/Admin/Elementor/User_Dashboard.php:145, ../includes/Admin/Elementor/User_Dashboard.php:160, ../includes/Admin/Elementor/User_Dashboard.php:172, ../includes/Admin/Elementor/User_Dashboard.php:184, ../includes/Admin/Elementor/User_Dashboard.php:196, ../includes/Admin/Elementor/User_Dashboard.php:1663, ../includes/Admin/Elementor/User_Dashboard.php:1675, ../includes/Admin/Elementor/User_Dashboard.php:1687, ../includes/Admin/Elementor/User_Dashboard.php:1699, ../includes/Admin/Elementor/User_Dashboard.php:1711, ../includes/Admin/Elementor/User_Dashboard.php:1862, ../includes/Admin/Elementor/User_Dashboard.php:1874, ../includes/Admin/Elementor/User_Dashboard.php:1886, ../includes/Admin/Elementor/User_Dashboard.php:1898, ../includes/Admin/Elementor/User_Dashboard.php:1910, ../includes/Admin/Elementor/User_Dashboard.php:1922, ../includes/Admin/Elementor/User_Dashboard.php:1934, ../includes/Admin/Elementor/User_Dashboard.php:1946
msgid "Hide"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:127, ../includes/Admin/Elementor/User_Dashboard.php:514
msgid "Avatar"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:142
msgid "Username"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:157, ../includes/Admin/Elementor/User_Dashboard.php:228, ../includes/Admin/Elementor/User_Dashboard.php:231, ../includes/Admin/Elementor/User_Dashboard.php:1651, ../includes/Admin/Elementor/User_Dashboard.php:1722
msgid "Dashboard"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:181, ../includes/Admin/Elementor/User_Dashboard.php:249, ../includes/Admin/Elementor/User_Dashboard.php:252
msgid "Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:193, ../includes/Admin/Elementor/User_Dashboard.php:686
msgid "Header"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:220
msgid "Label"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:259, ../includes/Admin/Elementor/User_Dashboard.php:262, ../includes/Admin/views/page-analytics.php:13
msgid "Refresh Stats"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:269
msgid "No Items"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:272, ../includes/Admin/views/better-payment-transaction-view.php:243, ../includes/Admin/views/template-transaction-list.php:161
msgid "No records found!"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:300
msgid "Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:437
msgid "Sidebar Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:588
msgid "Menu"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:840
msgid "Table"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:849
msgid "Table Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:969
msgid "Table Header"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1122
msgid "Table Body"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1276
msgid "Table Body » Buttons"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1285
msgid "Active Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1353
msgid "Inactive Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1421
msgid "Cancel Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1660
msgid "Transaction Summary"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1672
msgid "Analytics Report"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1684, ../includes/Admin/Elementor/User_Dashboard.php:1809, ../includes/Admin/Elementor/User_Dashboard.php:1812
msgid "Recent Transactions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1696
msgid "Recurring Subscription"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1708
msgid "Split Subscription"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1731, ../includes/Admin/Elementor/User_Dashboard.php:1734, ../includes/Admin/views/template-email-notification.php:255
msgid "Total Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1744, ../includes/Admin/Elementor/User_Dashboard.php:1747
msgid "Completed Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1757, ../includes/Admin/Elementor/User_Dashboard.php:1760
msgid "Incomplete Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1770, ../includes/Admin/Elementor/User_Dashboard.php:1773
msgid "Refunded Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1783, ../includes/Admin/Elementor/User_Dashboard.php:1786
msgid "View All"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1796, ../includes/Admin/Elementor/User_Dashboard.php:1799
msgid "Analytics Reports"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1822, ../includes/Admin/Elementor/User_Dashboard.php:1825
msgid "Recurring Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1835, ../includes/Admin/Elementor/User_Dashboard.php:1838
msgid "Split Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1850, ../includes/Admin/Elementor/User_Dashboard.php:1969
msgid "Transactions List"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1859, ../includes/Admin/Elementor/User_Dashboard.php:1978, ../includes/Admin/Elementor/User_Dashboard.php:1981, ../includes/Admin/views/template-transaction-list.php:32
msgid "Name"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1919, ../includes/Admin/Elementor/User_Dashboard.php:2043, ../includes/Admin/Elementor/User_Dashboard.php:2046, ../includes/Admin/views/better-payment-transaction-list.php:272, ../includes/Admin/views/better-payment-transaction-list.php:275, ../includes/Admin/views/template-transaction-list.php:47
msgid "Source"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1931, ../includes/Admin/Elementor/User_Dashboard.php:2056, ../includes/Admin/Elementor/User_Dashboard.php:2059, ../includes/Admin/views/better-payment-transaction-list.php:179, ../includes/Admin/views/better-payment-transaction-list.php:198, ../includes/Admin/views/template-transaction-list.php:50
msgid "Status"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1943, ../includes/Admin/Elementor/User_Dashboard.php:2069, ../includes/Admin/Elementor/User_Dashboard.php:2072, ../includes/Admin/views/template-transaction-list.php:53
msgid "Date"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:29
msgid "Save Changes"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:47
msgid "Go Premium"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:53
msgid "License"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:64
msgid "General"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:69
msgid "Admin Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:70
msgid "Customer Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:74
msgid "Payment"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:78, ../includes/Admin/views/better-payment-settings.php:119, ../includes/Admin/views/better-payment-transaction-list.php:278, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:32, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:48, ../includes/Admin/views/elementor/layouts/layout-1.php:98, ../includes/Admin/views/elementor/layouts/layout-2.php:66
msgid "Paystack"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:90, ../includes/Admin/views/better-payment-settings.php:106, ../includes/Admin/views/better-payment-settings.php:122, ../includes/Admin/views/better-payment-settings.php:139
msgid "See documentation."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:121
msgid "Enable Paystack if you want to accept payment via Paystack."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:138
msgid "Enable User Dashboard widget. It shows list of transactions and subscriptions for the user."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:187
msgid "Email address"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:188
msgid "Enter website admin email address here. This email will be used to send email notification for each transaction."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:194, ../includes/Admin/views/better-payment-settings.php:270
msgid "Email subject"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:195
msgid "Email subject for the admin email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:202
msgid "Email body for the admin email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:206, ../includes/Admin/views/better-payment-settings.php:282
msgid "Additional Headers"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:209, ../includes/Admin/views/better-payment-settings.php:285
msgid "From Name"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:212, ../includes/Admin/views/better-payment-settings.php:288
msgid "From name that will be used in the email headers."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:216, ../includes/Admin/views/better-payment-settings.php:292
msgid "From Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:219, ../includes/Admin/views/better-payment-settings.php:295
msgid "Email address that will be displayed in the email header as From Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:226, ../includes/Admin/views/better-payment-settings.php:302
msgid "Email address that will be displayed in the email header as Reply-To Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:233, ../includes/Admin/views/better-payment-settings.php:309
msgid "Email address that will be displayed in the email header as Cc Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:240, ../includes/Admin/views/better-payment-settings.php:316
msgid "Email address that will be displayed in the email header as Bcc Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:244, ../includes/Admin/views/better-payment-settings.php:320
msgid "Send As"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:248, ../includes/Admin/views/better-payment-settings.php:324
msgid "Select One"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:250, ../includes/Admin/views/better-payment-settings.php:326
msgid "Html"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:253, ../includes/Admin/views/better-payment-settings.php:329
msgid "Html helps to send html markup in the email body. Select plain if you just want plain text in the email body."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:264
msgid "Customer email address will be auto populated from payment form. This email will be used to send email notification for each transaction."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:271
msgid "Email subject for the customer (who make payments) email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:278
msgid "Email body for the customer email notification. "
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:364
msgid "Live Client ID"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:366
msgid "PayPal live client ID is required to do Refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:367, ../includes/Admin/views/better-payment-settings.php:379, ../includes/Admin/views/better-payment-settings.php:391, ../includes/Admin/views/better-payment-settings.php:403, ../includes/Admin/views/better-payment-settings.php:437, ../includes/Admin/views/better-payment-settings.php:449, ../includes/Admin/views/better-payment-settings.php:461, ../includes/Admin/views/better-payment-settings.php:473, ../includes/Admin/views/better-payment-settings.php:506, ../includes/Admin/views/better-payment-settings.php:518, ../includes/Admin/views/better-payment-settings.php:530, ../includes/Admin/views/better-payment-settings.php:542
msgid "see documentation."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:376
msgid "Live Secret"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:378
msgid "PayPal live secret is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:388
msgid "Test/Sandbox Client ID"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:390
msgid "PayPal test/sandbox client id is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:400
msgid "Test/Sandbox Secret"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:402
msgid "PayPal test/sandbox secret is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:488
msgid "Live mode allows you to process real transactions. It just requires live Paystack keys (public and secret keys) to accept real payments."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:505
msgid "Paystack live public key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:517
msgid "Paystack live secret key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:529
msgid "Paystack test public key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:541
msgid "Paystack test secret key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:589, ../includes/Admin/views/better-payment-transaction-list.php:123, ../includes/Admin/views/page-analytics.php:30
msgid "Total Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:598, ../includes/Admin/views/better-payment-transaction-list.php:135, ../includes/Admin/views/page-analytics.php:49
msgid "Completed Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:607, ../includes/Admin/views/better-payment-transaction-list.php:147, ../includes/Admin/views/page-analytics.php:68
msgid "Incomplete Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:619, ../includes/Admin/views/better-payment-settings.php:621
msgid "Documentation"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:620
msgid "Get started by spending some time with the documentation to get familiar with Better Payment."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:631
msgid "Contribute to Better Payment"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:632
msgid "You can contribute to make Better Payment better reporting bugs, creating issues, pull requests at "
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:633
msgid "Report a Bug"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:643
msgid "Need Help?"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:644
msgid "Stuck with something? Get help from live chat or support ticket."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:645
msgid "Initiate a Chat"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:654
msgid "Show Your Love"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:657
msgid "We love to have you in Better Payment family. We are making it more awesome everyday. Take your 2 minutes to review the plugin and spread the love to encourage us to keep it going."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:658
msgid "Leave a Review"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:38
msgid "Import Data"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:39
msgid "Export All"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:63
msgid "Choose csv file…"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:67
msgid "No file chosen"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:73
msgid "Let's Go!"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:78
msgid "Upload any csv file that is exported from another site via Better Payment."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:159, ../includes/Admin/views/better-payment-transaction-list.php:161, ../includes/Admin/Elementor/Controls/Select2.php:27
msgid "Search"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:161
msgid "Search by email, amount, transaction id, source"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:165
msgid "From Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:167
msgid "Date Range"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:172, ../includes/Admin/views/better-payment-transaction-list.php:174
msgid "To Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:182, ../includes/Admin/views/better-payment-transaction-list.php:199
msgid "All"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:200
msgid "Completed"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:201
msgid "Incomplete"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:202
msgid "Refunded"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:223, ../includes/Admin/views/better-payment-transaction-list.php:226
msgid "Sort By"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:227
msgid "Payment Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:250, ../includes/Admin/views/better-payment-transaction-list.php:253
msgid "Sort Order"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:254
msgid "Descending"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:255
msgid "Ascending"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:295
msgid "Filter"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:296
msgid "Reset"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:303
msgid "Custom Date Range"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:311
msgid "Start Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:316
msgid "Select start date of desired time period to see the analytics."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:324
msgid "End Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:329
msgid "Select end date of desired time period to see the analytics."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:337
msgid "Confirm"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:338
msgid "Cancel"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:41
msgid "Back to Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:54, ../includes/Admin/views/better-payment-transaction-view.php:55, ../includes/Admin/views/better-payment-transaction-view.php:58, ../includes/Admin/views/better-payment-transaction-view.php:62, ../includes/Admin/views/better-payment-transaction-view.php:65, ../includes/Admin/views/better-payment-transaction-view.php:67
msgid ""
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:82, ../includes/Admin/views/better-payment-transaction-view.php:96, ../includes/Admin/views/better-payment-transaction-view.php:97, ../includes/Admin/views/template-transaction-list.php:142, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:168
msgid "N/A"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:98
msgid "#"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:111
msgid "Basic Information"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:118
msgid "Name:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:120
msgid "Email Address:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:122, ../includes/Admin/views/better-payment-transaction-view.php:139, ../includes/Admin/views/better-payment-transaction-view.php:211, ../includes/Admin/views/template-transaction-list.php:104, ../includes/Admin/views/template-transaction-list.php:121, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:122, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:143
msgid "Copy"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:126
msgid "Single Amount:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:127
msgid "Quantity:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:128
msgid "Total Amount:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:135, ../includes/Admin/views/better-payment-transaction-view.php:209
msgid "Transaction ID:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:145
msgid "Source:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:147
msgid "Status:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:156
msgid "Mark as Completed"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:163
msgid "Additional Information"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:166
msgid "Order ID:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:167
msgid "Payment Date:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:168
msgid "Referer Page:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:169
msgid "Referer Widget:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:179
msgid "Payment Gateway"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:201, ../includes/Admin/views/better-payment-transaction-view.php:194, ../includes/Admin/views/better-payment-transaction-view.php:187
msgid "Payment Method:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:216
msgid "Email Activity"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:221
msgid "Email sent to"
msgstr ""

#: ../includes/Admin/views/page-analytics.php:13
msgid "We are caching all data for 1 hour. To see the live data press this button!"
msgstr ""

#: ../includes/Admin/views/page-analytics.php:87
msgid "Refund Transactions"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:166
msgid "email-tick"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:174
msgid "This is to acknowledge that we have received the payment of %s %s on %s"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:169
msgid "Great News! Admin"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:170
msgid "You have received a new transaction through"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:192
msgid "Transaction ID - "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:197
msgid "Date : "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:213
msgid "From"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:276
msgid "Payment Method : "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:308
msgid "Transaction Summary:"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:319
msgid "Description"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:322
msgid "Rate"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:325
msgid "Qty"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:361
msgid "Total:"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:376
msgid "You can also find the transaction details by visiting the link below."
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:377
msgid "View Transaction"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:381
msgid "Powered By"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:56
msgid "Action"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:99, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:116
msgid "Imported"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:152, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:184
msgid "View"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:153
msgid "Delete"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:174
msgid "10"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:175
msgid "20"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:176
msgid "50"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:177
msgid "100"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:28
msgid "Remove"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:29
msgid "Image"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:30
msgid "Title"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:31
msgid "Price"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:32
msgid "Quantity"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:33
msgid "Subtotal"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:251
msgid "Both"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:252
msgid "Input Field"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:266
msgid "Don't forget to enable the <strong>Payment Amount</strong> (& Show Amount List) field from Better Payment Section below"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:278
msgid "Placeholder"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:353
msgid "The value must be less than or equal to %s"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:357
msgid "The value must be greater than or equal %s"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:56
msgid "Don't forget to add PayPal or Stripe on <strong>Actions After Submit</strong>"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:77
msgid "Payment Amount Field"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:78
msgid "We add an extra field type <b>Payment Amount</b> which offers you to accept payment via Paypal and Stripe. Disable it if you want to hide the field type.<br><br>"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:134
msgid "Form Fields => Payment Amount => <b>Field Type</b> helps to show Amount List with or without Input field."
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:174
msgid "Field Style"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:250
msgid "Border Width"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:281
msgid "Amount List Style"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:68
msgid "Currency is not supported by PayPal!"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:61
msgid "Currency Symbols"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:72
msgid "Currency is not supported by Paystack!"
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:4
msgid "Why upgrade to Premium Version?"
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:5
msgid "Get access to Analytics, Refund, Invoice & many more features that makes your life way easier. You will also get world class support from our dedicated team 24/7."
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:6
msgid "Get Premium Version"
msgstr ""
