<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '4ea782b7c5233d57851249b997f5830c15da9296',
        'name' => 'better-payment/better-payment',
        'dev' => true,
    ),
    'versions' => array(
        'better-payment/better-payment' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '4ea782b7c5233d57851249b997f5830c15da9296',
            'dev_requirement' => false,
        ),
        'league/csv' => array(
            'pretty_version' => '9.8.0',
            'version' => '9.8.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/csv',
            'aliases' => array(),
            'reference' => '9d2e0265c5d90f5dd601bc65ff717e05cec19b47',
            'dev_requirement' => false,
        ),
        'priyomukul/wp-notice' => array(
            'pretty_version' => 'v2.x-dev',
            'version' => '2.9999999.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../priyomukul/wp-notice',
            'aliases' => array(),
            'reference' => '5471f3262e583ebcba56062d84faecc910bd04d6',
            'dev_requirement' => false,
        ),
    ),
);
