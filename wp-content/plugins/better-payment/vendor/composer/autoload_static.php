<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit2ee63c42571df996aa8fe4baf5c16be6
{
    public static $files = array (
        '9e4824c5afbdc1482b6025ce3d4dfde8' => __DIR__ . '/..' . '/league/csv/src/functions_include.php',
        '008714da6a16824688fb4ec5a1101c20' => __DIR__ . '/../..' . '/includes/functions.php',
    );

    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PriyoMukul\\WPNotice\\' => 20,
        ),
        'L' => 
        array (
            'League\\Csv\\' => 11,
        ),
        'B' => 
        array (
            'Better_Payment\\Lite\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PriyoMukul\\WPNotice\\' => 
        array (
            0 => __DIR__ . '/..' . '/priyomukul/wp-notice/src',
        ),
        'League\\Csv\\' => 
        array (
            0 => __DIR__ . '/..' . '/league/csv/src',
        ),
        'Better_Payment\\Lite\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit2ee63c42571df996aa8fe4baf5c16be6::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit2ee63c42571df996aa8fe4baf5c16be6::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit2ee63c42571df996aa8fe4baf5c16be6::$classMap;

        }, null, ClassLoader::class);
    }
}
