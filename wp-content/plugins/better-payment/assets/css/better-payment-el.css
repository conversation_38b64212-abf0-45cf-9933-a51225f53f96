/*!***********************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/postcss-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/css/better-payment-el.scss ***!
  \***********************************************************************************************************************************************************************************************/
@import url(https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap);
/*!***************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/postcss-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/css/better-payment-el.scss (1) ***!
  \***************************************************************************************************************************************************************************************************/
.better-payment--wrapper {
  overflow: hidden;
  font-family: "Poppins", sans-serif;
}

.better-payment--wrapper * {
  outline: none;
  border: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

label {
  margin-bottom: 0;
}

button {
  vertical-align: middle;
  background: unset;
  font-family: "Poppins", sans-serif;
  font-weight: 500 !important;
}

button:hover, button:focus {
  outline: none;
}

button:not(:disabled) {
  cursor: pointer;
}

.pt140 {
  padding-top: 140px;
}

.pb230 {
  padding-bottom: 230px;
}

.better-payment--wrapper .background__grey {
  background: #f4f5f7;
}

.better-payment--wrapper .background__red {
  background: #ff6161;
}

.better_payment__form--one {
  max-width: 835px;
  margin: 0 auto;
  position: relative;
  padding-top: 100px;
}

@media (max-width: 991.98px) {
  .better_payment__form--one {
    padding-top: 50px;
  }
}
.better_payment__form--one:before {
  position: absolute;
  background: #252aff;
  top: 0;
  left: 0;
  right: 0;
  height: 380px;
  content: "";
  border-radius: 10px;
}

.better_payment__form--one form {
  background: #ffffff;
  margin: 0 75px;
  position: relative;
  top: 2;
  padding: 60px;
  padding-bottom: 105px;
  border-radius: 5px;
}

@media (max-width: 991.98px) {
  .better_payment__form--one form {
    margin: 0 35px;
    padding: 50px;
  }
}
@media (max-width: 575.98px) {
  .better_payment__form--one form {
    padding: 30px;
  }
}
.better_payment__form--one form .bp-form__group {
  margin-bottom: 30px;
}

.better_payment__form--one form .bp-form__group .bp-form__control {
  height: 75px;
  width: 100%;
  border-radius: 5px;
  background: #ffffff;
  font-size: 20px;
  font-weight: 400;
  color: #252525;
  border-width: 1px;
  border-color: #c2c2c2;
  border-style: solid;
  padding: 0 30px;
}

.better_payment__form--one form .bp-form__group .bp-form__control::-webkit-input-placeholder {
  color: #707070;
}

.better_payment__form--one form .bp-form__group .bp-form__control::-moz-placeholder {
  color: #707070;
}

.better_payment__form--one form .bp-form__group .bp-form__control:-ms-input-placeholder {
  color: #707070;
}

.better_payment__form--one form .bp-form__group .bp-form__control:-moz-placeholder {
  color: #707070;
}

@media (max-width: 991.98px) {
  .better_payment__form--one form .bp-form__group .bp-form__control {
    height: 60px;
    font-size: 18px;
  }
}
.better_payment__form--one form .bp-payment-amount-wrap,
.payment-form-layout .bp-payment-amount-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  column-gap: 20px;
}

.better_payment__form--one form .bp-payment-amount-wrap .bp-form__group,
.payment-form-layout .bp-payment-amount-wrap .bp-form__group {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(25% - 15px);
  flex: 0 0 calc(25% - 15px);
}

@media (max-width: 575.98px) {
  .better_payment__form--one form .bp-payment-amount-wrap .bp-form__group,
  .payment-form-layout .bp-payment-amount-wrap .bp-form__group {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(50% - 10px);
    flex: 0 0 calc(50% - 10px);
  }
}
.better_payment__form--one form .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control,
.payment-form-layout .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control {
  display: none;
}

.better_payment__form--one form .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control:checked ~ label,
.payment-form-layout .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control:checked ~ label {
  border-color: #252aff;
  background: rgba(37, 42, 255, 0.1);
  color: #252aff;
}

.better_payment__form--one form .bp-payment-amount-wrap .bp-form__group label,
.payment-form-layout .bp-payment-amount-wrap .bp-form__group label {
  height: 60px;
  width: 100%;
  border-radius: 5px;
  background: #ffffff;
  font-size: 20px;
  font-weight: 600;
  color: #252525;
  border-width: 1px;
  border-color: #c2c2c2;
  border-style: solid;
  padding: 0 30px;
  line-height: 60px;
  padding: 0;
  text-align: center;
  display: inline-block;
  cursor: pointer;
}

@media (max-width: 991.98px) {
  .better_payment__form--one form .bp-payment-amount-wrap .bp-form__group label,
  .payment-form-layout .bp-payment-amount-wrap .bp-form__group label {
    line-height: 60px;
    height: 60px;
    font-size: 18px;
  }
}
@media (max-width: 991.98px) {
  .better_payment__form--one form .bp-payment-amount-wrap .bp-form__group .bp-form__control,
  .payment-form-layout .bp-payment-amount-wrap .bp-form__group .bp-form__control {
    padding: 0 20px;
  }
}
.better_payment__form--one form .payment__option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

@media (max-width: 575.98px) {
  .better_payment__form--one form .payment__option {
    display: block;
  }
  .better_payment__form--one form .payment__option .button1:not(:last-child) {
    margin-bottom: 25px;
  }
  .better_payment__form--one form .payment__option .button1 {
    width: 100%;
  }
}
.better_payment__form--one form .payment__option .button1 {
  height: 75px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(50% - 15px);
  flex: 0 0 calc(50% - 15px);
  border: 1px solid #252aff;
  border-radius: 5px;
  padding: 0;
  font-size: 20px;
  font-weight: 600;
  color: #252525;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: rgba(37, 42, 255, 0.1);
  text-transform: none;
  text-decoration: none;
}

.better_payment__form--one form .payment__option .button1 img {
  margin-right: 10px;
}

.better_payment__form--one form .payment__option .button1.button1__selected {
  background: #252aff;
  color: #ffffff;
}

@media (max-width: 991.98px) {
  .better_payment__form--one form .payment__option .button1 {
    height: 60px;
    font-size: 18px;
  }
}
.better_payment__form--two {
  max-width: 835px;
  margin: 0 auto;
  position: relative;
  padding-top: 100px;
}

@media (max-width: 991.98px) {
  .better_payment__form--two {
    padding-top: 50px;
  }
}
.better_payment__form--two:before {
  position: absolute;
  background: #30d19e;
  top: 0;
  left: 0;
  right: 0;
  height: 380px;
  content: "";
  border-radius: 10px;
}

.better_payment__form--two form {
  background: #ffffff;
  margin: 0 75px;
  position: relative;
  top: 2;
}

.better_payment__form--two form .input__wrap {
  padding: 60px;
  padding-bottom: 0;
}

@media (max-width: 991.98px) {
  .better_payment__form--two form .input__wrap {
    padding: 50px;
    padding-bottom: 0;
  }
}
@media (max-width: 575.98px) {
  .better_payment__form--two form .input__wrap {
    padding: 30px;
    padding-bottom: 0;
  }
}
@media (max-width: 991.98px) {
  .better_payment__form--two form {
    margin: 0 35px;
  }
}
.better_payment__form--two form .bp-form__group {
  margin-bottom: 30px;
}

.better_payment__form--two form .bp-form__group .bp-form__control {
  height: 75px;
  width: 100%;
  border-radius: 40px;
  background: #ffffff;
  font-size: 20px;
  font-weight: 400;
  color: #252525;
  border-width: 1px;
  border-color: #c2c2c2;
  border-style: solid;
  padding: 0 30px;
}

.better_payment__form--two form .bp-form__group .bp-form__control::-webkit-input-placeholder {
  color: #707070;
}

.better_payment__form--two form .bp-form__group .bp-form__control::-moz-placeholder {
  color: #707070;
}

.better_payment__form--two form .bp-form__group .bp-form__control:-ms-input-placeholder {
  color: #707070;
}

.better_payment__form--two form .bp-form__group .bp-form__control:-moz-placeholder {
  color: #707070;
}

@media (max-width: 991.98px) {
  .better_payment__form--two form .bp-form__group .bp-form__control {
    height: 60px;
    font-size: 18px;
  }
}
.better_payment__form--two form .bp-payment-amount-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  column-gap: 15px;
}

.better_payment__form--two form .bp-payment-amount-wrap .bp-form__group {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(25% - 22.5px);
  flex: 0 0 calc(25% - 22.5px);
}

@media (max-width: 575.98px) {
  .better_payment__form--two form .bp-payment-amount-wrap .bp-form__group {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(50% - 10px);
    flex: 0 0 calc(50% - 10px);
  }
}
.better_payment__form--two form .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control {
  display: none;
}

.better_payment__form--two form .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control:checked ~ label {
  border-color: #30d19e;
  background: #30d19e;
  color: #ffffff;
}

.better_payment__form--two form .bp-payment-amount-wrap .bp-form__group label {
  height: 60px;
  width: 100%;
  border-radius: 40px;
  background: #ffffff;
  font-size: 20px;
  font-weight: 600;
  color: #252525;
  border-width: 1px;
  border-color: #c2c2c2;
  border-style: solid;
  padding: 0 30px;
  line-height: 60px;
  padding: 0;
  text-align: center;
  display: inline-block;
  cursor: pointer;
}

@media (max-width: 991.98px) {
  .better_payment__form--two form .bp-payment-amount-wrap .bp-form__group label {
    line-height: 60px;
    height: 60px;
    font-size: 18px;
  }
}
@media (max-width: 991.98px) {
  .better_payment__form--two form .bp-payment-amount-wrap .bp-form__group .bp-form__control {
    padding: 0 20px;
  }
}
.better_payment__form--two form .payment__option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

@media (max-width: 575.98px) {
  .better_payment__form--two form .payment__option {
    font-size: 16px;
  }
}
.better_payment__form--two form .payment__option .button1 {
  height: 75px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  border: 0;
  padding: 0;
  font-size: 20px;
  font-weight: 600;
  color: #252525;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}

.better_payment__form--two form .payment__option .button1:before {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: #adb9db;
  content: "";
}

.better_payment__form--two form .payment__option .button1.button1__selected {
  color: #30d19e;
}

.better_payment__form--two form .payment__option .button1.button1__selected:before {
  background: #30d19e;
}

@media (max-width: 991.98px) {
  .better_payment__form--two form .payment__option .button1 {
    height: 60px;
    font-size: 18px;
  }
}
.better_payment__form--three {
  max-width: 690px;
  margin: 0 auto;
  position: relative;
}

.better_payment__form--three form {
  background: #ffebeb;
  position: relative;
  top: 2;
  padding: 60px;
  padding-bottom: 105px;
  border-radius: 5px;
}

@media (max-width: 991.98px) {
  .better_payment__form--three form {
    margin: 0 35px;
    padding: 50px;
  }
}
@media (max-width: 575.98px) {
  .better_payment__form--three form {
    padding: 30px;
  }
}
.better_payment__form--three form .bp-form__group {
  margin-bottom: 30px;
  position: relative;
}

.better_payment__form--three form .bp-form__group .bp-form__control {
  height: 75px;
  width: 100%;
  border-radius: 0;
  background: transparent;
  font-size: 20px;
  font-weight: 400;
  color: #252525;
  border: 0;
  border-bottom: 1px solid #c2c2c2;
  padding: 0 30px;
}

.better_payment__form--three form .bp-form__group .bp-form__control::-webkit-input-placeholder {
  color: #707070;
}

.better_payment__form--three form .bp-form__group .bp-form__control::-moz-placeholder {
  color: #707070;
}

.better_payment__form--three form .bp-form__group .bp-form__control:-ms-input-placeholder {
  color: #707070;
}

.better_payment__form--three form .bp-form__group .bp-form__control:-moz-placeholder {
  color: #707070;
}

@media (max-width: 991.98px) {
  .better_payment__form--three form .bp-form__group .bp-form__control {
    height: 60px;
    font-size: 18px;
    padding: 0 20px;
  }
}
@media (max-width: 575.98px) {
  .better_payment__form--three form .bp-form__group .bp-form__control {
    padding: 0;
  }
}
.better_payment__form--three form .bp-form__group .bp-form__control:focus ~ label {
  color: #ff6161;
}

.better_payment__form--three form .bp-form__group input[type=text].bp-form__control:valid ~ label {
  display: none;
}

.better_payment__form--three form .bp-form__group label {
  position: absolute;
  top: 25px;
  left: 30px;
  color: #707070;
  font-size: 20px;
}

.better_payment__form--three form .bp-form__group label i {
  margin-right: 15px;
}

@media (max-width: 991.98px) {
  .better_payment__form--three form .bp-form__group label {
    top: 17.5px;
    font-size: 18px;
    left: 20px;
  }
}
@media (max-width: 575.98px) {
  .better_payment__form--three form .bp-form__group label {
    left: 0;
  }
}
.better_payment__form--three form .bp-payment-amount-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  column-gap: 15px;
}

.better_payment__form--three form .bp-payment-amount-wrap .bp-form__group {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(25% - 22.5px);
  flex: 0 0 calc(25% - 22.5px);
}

@media (max-width: 575.98px) {
  .better_payment__form--three form .bp-payment-amount-wrap .bp-form__group {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc(50% - 10px);
    flex: 0 0 calc(50% - 10px);
  }
}
.better_payment__form--three form .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control {
  display: none;
}

.better_payment__form--three form .bp-payment-amount-wrap .bp-form__group input[type=radio].bp-form__control:checked ~ label {
  border-color: #ff6161;
  background: #ff6161;
  color: #ffffff;
}

.better_payment__form--three form .bp-payment-amount-wrap .bp-form__group label {
  position: static;
  height: 60px;
  width: 100%;
  border-radius: 5px;
  background: transparent;
  font-size: 20px;
  font-weight: 600;
  color: #252525;
  border-width: 1px;
  border-color: #c2c2c2;
  border-style: solid;
  padding: 0 30px;
  line-height: 60px;
  padding: 0;
  text-align: center;
  display: inline-block;
  cursor: pointer;
}

@media (max-width: 991.98px) {
  .better_payment__form--three form .bp-payment-amount-wrap .bp-form__group label {
    line-height: 60px;
    height: 60px;
    font-size: 18px;
  }
}
.better_payment__form--three form .bp-payment-amount-wrap .bp-form__group .bp-form__control {
  border: 1px solid #c2c2c2;
  border-radius: 5px;
}

@media (max-width: 991.98px) {
  .better_payment__form--three form .bp-payment-amount-wrap .bp-form__group .bp-form__control {
    padding: 0 20px;
  }
}
.better_payment__form--three form .payment__option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

@media (max-width: 575.98px) {
  .better_payment__form--three form .payment__option {
    display: block;
  }
  .better_payment__form--three form .payment__option .button1:not(:last-child) {
    margin-bottom: 25px;
  }
}
.better_payment__form--three form .payment__option .button1 {
  height: 75px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(50% - 15px);
  flex: 0 0 calc(50% - 15px);
  border: 1px solid #ff6161;
  border-radius: 5px;
  padding: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #ff6161;
}

.better_payment__form--three form .payment__option .button1 img {
  margin-right: 10px;
}

.better_payment__form--three form .payment__option .button1.button1__selected {
  background: #ff6161;
  color: #ffffff;
}

@media (max-width: 991.98px) {
  .better_payment__form--three form .payment__option .button1 {
    height: 60px;
    font-size: 18px;
  }
}
.better-payment-payment__report {
  background: #fff;
  border-radius: 10px;
  -webkit-box-shadow: 51.43px 30.902px 125px 0px rgba(0, 9, 78, 0.11);
  box-shadow: 51.43px 30.902px 125px 0px rgba(0, 9, 78, 0.11);
  text-align: center;
  width: calc(100% - 30px);
  max-width: 870px;
  margin: 0 auto;
  padding: 80px 30px;
}

.better-payment-payment__report.better-payment-success-report {
  width: unset;
}

.bp-thank_page span {
  display: inline-block;
}

.bp-bold {
  font-weight: 400;
  color: #222b3d;
}

.bp-flex {
  display: flex;
}

.bp-thank_page {
  height: 100vh;
  display: flex;
  align-items: center;
  padding: 20px;
}

.bp-thank_page-wrapper {
  background-color: #ffffff;
  max-width: 700px;
  margin: 0 auto;
  vertical-align: middle;
  padding: 40px;
  border-radius: 20px;
  width: 100%;
  box-shadow: 0px 0px 10px 3px rgba(34, 43, 61, 0.1019607843);
}

.bp-thank_page-logo {
  text-align: center;
  margin-bottom: 32px;
}

.bp-thank_page-logo_wrapper {
  display: inline-block;
  background: rgba(107, 89, 238, 0.1019607843);
  border-radius: 16px;
  padding: 11px;
}

.bp-thank_page-text {
  text-align: center;
  border-bottom: 1px solid #dfe2ed;
}

.bp-page_header {
  font-size: 32px;
  color: #222b3d;
  font-weight: 500;
  margin-bottom: 8px;
}

.bp-reason_for-text {
  font-size: 24px;
  font-weight: 300;
  color: #222b3d;
  margin-bottom: 8px;
}

.bp-payment_info {
  color: #6a758c;
  font-size: 16px;
  font-weight: 300;
  margin-top: 8px;
}

.bp-transaction_info-wrapper {
  background-color: #f0f3f7;
  border: 1px solid #dfe2ed;
  min-width: 258px;
  width: auto;
  padding: 7px;
  border-radius: 4px;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 27px;
  position: relative;
  display: inline-flex;
}

.bp-transaction_info {
  color: #6a758c;
  font-size: 14px;
  font-weight: 300;
  align-items: center;
  gap: 4px;
  margin: 0;
}

.bp-transaction_id {
  position: relative;
  width: 95px;
  overflow: hidden;
}

.bp-transaction_id::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(90deg, rgba(240, 243, 247, 0.0588235294), #f0f3f7);
}

.bp-thank_page-info {
  border-bottom: 1px solid #dfe2ed;
  margin-bottom: 32px;
}

.bp-transaction_data-copy_btn {
  border: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  width: 20px;
  height: 20px;
  z-index: 999;
  stroke: #394357;
  padding: 0px;
}
.bp-transaction_data-copy_btn:hover {
  background: transparent;
}
.bp-transaction_data-copy_btn:focus {
  background: none;
}

.copied-text::after,
.bp-transaction_data-copy_btn::after {
  display: none;
  content: "Copy";
  position: absolute;
  top: -28px;
  right: -18px;
  background-color: white;
  padding: 6px 10px;
  border-radius: 4px;
  box-shadow: 0px 5px 10px 0px rgba(34, 43, 61, 0.2980392157);
  font-size: 12px;
  font-weight: 400;
  color: #222b3d;
}

.copied-text {
  stroke: #6b59ee;
}

.copied-text::after {
  display: block;
  content: "Copied!";
  right: -23px;
}

.copied-text::before,
.bp-transaction_data-copy_btn::before {
  display: none;
  content: "";
  position: absolute;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #ffffff;
  bottom: -6px;
  right: 5px;
  top: 0px;
  z-index: 1;
}

.copied-text::before {
  display: block;
}

.bp-transaction_data-copy_btn:hover::before,
.bp-transaction_data-copy_btn:hover::after {
  display: block;
}

.bp-transaction_data-copy_btn:active svg {
  width: 17px;
  height: 17px;
}

.bp-page_info-list {
  padding: 32px 0;
}

.bp-page_info-list_item {
  color: #6a758c;
  font-weight: 300;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 20px;
}

.bp-info_content {
  font-size: 20px;
}

.bp-payment_method-logo-wrapper {
  height: 30px;
  width: auto;
  line-height: 0;
}

.bp-payment_method-logo {
  height: 100% !important;
  object-fit: cover;
}

.bp-button_group {
  justify-content: space-between;
  align-items: center;
}

.bp-print_btn {
  background-color: #f4f5ff;
  border-radius: 8px;
  padding: 13px 16px;
  border: none;
  cursor: pointer;
  align-items: center;
  font-weight: 400;
  font-size: 16px;
  color: #6b59ee;
  gap: 8px;
}
.bp-print_btn:hover {
  background-color: #f4f5ff;
  color: #6b59ee;
}
.bp-print_btn:focus {
  background-color: #f4f5ff;
  color: #6b59ee;
}

.bp-print_btn:active {
  transform: scale(0.96);
}

.bp-btn_wrapper {
  gap: 8px;
}

.bp-details_btn {
  padding: 13px 16px;
  color: #f7f8fa;
  background-color: #6b59ee;
  font-weight: 400;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bp-details_btn:hover {
  background-color: #624fec;
  color: #f7f8fa;
}

@media (max-width: 991.98px) {
  .bp-thank_page-wrapper {
    padding: 20px;
  }
  .bp-page_header {
    font-size: 28px;
  }
  .bp-reason_for-text {
    font-size: 20px;
  }
  .bp-payment_info {
    color: #6a758c;
    font-size: 13px;
  }
  .bp-page_info-list {
    padding: 28px 0;
  }
  .bp-info_content {
    font-size: 16px;
  }
  .bp-print_btn {
    padding: 10px 13px;
    font-size: 16px;
  }
  .bp-print_btn:focus {
    background-color: #f4f5ff;
    color: #6b59ee;
  }
  .bp-details_btn {
    padding: 10px 13px;
    font-size: 16px;
  }
}
/* Hide everything except print-section */
@media print {
  .bp-thank_page {
    height: unset;
    padding: 0;
  }
  .bp-button_group {
    display: none;
  }
}
.better-payment-payment__report .report__thumb {
  /* margin-bottom: 20px; */
}

.better-payment-payment__report .report__thumb img {
  display: inline-block;
}

.better-payment-payment__report .report__content h3 {
  font-size: 40px;
  font-weight: 500;
  font-family: "Poppins", sans-serif;
  margin-bottom: 30px;
}

@media (max-width: 767.98px) {
  .better-payment-payment__report .report__content h3 {
    font-size: 32px;
  }
}
.better-payment-payment__report .report__content h3.transaction__success {
  color: #2a3256;
}

.better-payment-success-transaction-number {
  background: #f4f6ff;
  border: 1px solid #e4e9ff;
  border-radius: 10px;
  padding: 5px 15px;
  margin-left: 10px;
  color: #006aff;
}

.better-payment-payment__report .report__content h3.transaction__failed {
  color: #f43333;
}

.better-payment-payment__report .report__content p {
  font-size: 20px;
  font-weight: 500;
  color: #2a3256;
  font-family: "Poppins", sans-serif;
  line-height: 1.8;
  margin: 0;
  margin-bottom: 15px;
}

@media (max-width: 767.98px) {
  .better-payment-payment__report .report__content p {
    font-size: 18px;
  }
}
.better-payment-form-layout.better-payment-hide-placeholder input::-webkit-input-placeholder {
  opacity: 0;
  visibility: hidden;
}

/* Common Pages Start */
.admin-bar .toast-top-right {
  top: 44px;
  right: 12px;
}

/* Common Pages End */
/** 
* Typography
*/
.font-size-xxs {
  font-size: 10px !important;
}

.font-size-xs {
  font-size: 12px !important;
}

.font-size-sm {
  font-size: 14px !important;
}

.font-size-md {
  font-size: 16px !important;
}

.font-size-lg {
  font-size: 18px !important;
}

.font-size-xl {
  font-size: 20px !important;
}

.font-size-xxl {
  font-size: 24px !important;
}

/*
* Forms
*/
.bp-input-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}

.bp-input-group-prepend {
  margin-right: -3px;
}

.bp-input-group-append, .bp-input-group-prepend {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.bp-input-group > .bp-input-group-prepend > .bp-input-group-text {
  border: 1px solid #818a91;
  border-right: none;
}

.bp-input-group-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
}

.elementor-field-group .elementor-field-textual.bp-elementor-field-textual-amount {
  width: unset;
  border-left: none;
}

.better-payment-success-report .report__thumb img,
.better-payment-error-report .report__thumb img {
  /* max-width: 300px; */
  width: 100px;
  /* margin-left: 5%; */
  /* margin-bottom: 40px; */
}

.better-payment-success-report .report__thumb i,
.better-payment-error-report .report__thumb i {
  font-size: 50px;
}

.line-height-0 {
  line-height: 0;
}
