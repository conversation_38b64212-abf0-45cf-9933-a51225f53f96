/*!******************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/postcss-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/css/setup-wizard.scss ***!
  \******************************************************************************************************************************************************************************************/
/* Setup Wizard Style */
.better-payment-setup-wizard-wrap {
  padding: 15px 9%;
}

.better-payment-setup-wizard {
  display: grid;
  grid-template-columns: repeat(5, 15%);
  grid-gap: 6.3%;
  position: relative;
  margin: 0 8%;
}

.better-payment-setup-wizard.four {
  grid-template-columns: repeat(4, 19%);
  grid-gap: 8%;
  margin: 0 10%;
}

.better-payment-setup-wizard li {
  border-radius: 5px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  text-align: center;
  padding: 15px 10px;
  position: relative;
  z-index: 1;
}

.better-payment-setup-wizard[data-step="0"] li:nth-child(-n+1),
.better-payment-setup-wizard[data-step="1"] li:nth-child(-n+2),
.better-payment-setup-wizard[data-step="2"] li:nth-child(-n+3),
.better-payment-setup-wizard[data-step="3"] li:nth-child(-n+4),
.better-payment-setup-wizard[data-step="4"] li:nth-child(-n+5) {
  border: 1px solid #a16bff;
}

.better-payment-setup-wizard[data-step="0"] li:nth-child(-n+1) .icon svg,
.better-payment-setup-wizard[data-step="1"] li:nth-child(-n+2) .icon svg,
.better-payment-setup-wizard[data-step="2"] li:nth-child(-n+3) .icon svg,
.better-payment-setup-wizard[data-step="3"] li:nth-child(-n+4) .icon svg,
.better-payment-setup-wizard[data-step="4"] li:nth-child(-n+5) .icon svg {
  fill: #a16bff;
}

.better-payment-setup-wizard:before,
.better-payment-setup-wizard:after {
  content: "";
  width: 100%;
  height: 5px;
  /* background-color: #ffffff; */
  background-color: #f5f5f5;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 0;
}

.better-payment-setup-wizard:before {
  width: 10%;
  left: 0;
  background: #a16bff;
  transition: width 0.5s;
  z-index: 1;
  border-radius: 5px;
}

.better-payment-setup-wizard[data-step="0"]::before {
  width: 17.8%;
}

.better-payment-setup-wizard[data-step="1"]::before {
  width: 39%;
}

.better-payment-setup-wizard[data-step="2"]::before {
  width: 61%;
}

.better-payment-setup-wizard[data-step="3"]::before {
  width: 82%;
}

.better-payment-setup-wizard[data-step="4"]::before {
  width: 100%;
}

/* four */
.better-payment-setup-wizard.four[data-step="0"]::before {
  width: 22.8%;
}

.better-payment-setup-wizard.four[data-step="1"]::before {
  width: 50%;
}

.better-payment-setup-wizard.four[data-step="2"]::before {
  width: 77%;
}

.better-payment-setup-wizard.four[data-step="3"]::before {
  width: 100%;
}

.step .icon svg,
.step .icon i {
  width: 20px;
  fill: #9ea3db;
}

.step .icon i {
  background: linear-gradient(97.4deg, #A293FF 0%, #6E58F7 100%);
  color: #fff;
}

.step .name {
  font-size: 14px;
  line-height: 1.2em;
  color: #4e4362;
  font-weight: 400;
  margin-top: 5px;
}

.better-payment-setup-final-info .better-payment-whatwecollecttext {
  margin: 0;
  display: none;
  font-size: 12px;
  margin-bottom: 5px;
}

.btn-collect:focus {
  outline: none;
}

.setup-content {
  display: none;
}

.setup-content.active {
  display: block;
}

.better-payment-elements-container + .better-payment-elements-cat {
  margin-top: 30px;
}

.better-payment-elements-container {
  width: 100%;
}

.better-payment-setup-wizard .better-payment-elements-info {
  justify-content: space-between;
  width: 100%;
}

.better-payment-setup-wizard-wrap .setup-content .better-payment-documentation-elements .better-payment-checkbox {
  flex-basis: 23% !important;
}

.better-payment-setup-wizard-wrap #integrations .row {
  grid-template-columns: repeat(3, 1fr);
}

/* body */
.better-payment-box {
  border-radius: 5px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  padding: 30px;
}

.better-payment-setup-body {
  margin-top: 30px;
  margin-bottom: 25px;
  min-height: unset !important;
}

.better-payment-setup-body img {
  max-width: 100%;
  width: 100%;
}

/* finalize */
#finalize h2 {
  font-size: 20px;
  text-align: center;
  color: #3f3f3f;
  margin: 18px 0;
}

#finalize p {
  margin: 10px 0 0;
  font-size: 15px;
  line-height: 1.4em;
  color: #5a5a5a;
  font-weight: 400;
  text-align: center;
}

.better-payment-iframe {
  position: relative;
  overflow: hidden;
  width: 80%;
  padding-top: 45.25%;
  margin: 25px auto;
  border-radius: 5px;
}

.better-payment-iframe * {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.better-payment-setup-final-info {
  margin-top: 20px;
  text-align: center;
}

.better-payment-setup-final-info .better-payment-input-group {
  margin-bottom: 15px;
}

.better-payment-setup-final-info input[type=checkbox] {
  padding: 10px !important;
  border: 3px solid #b7b7b7;
  background-color: #fff;
  box-shadow: none !important;
  border-radius: 2px;
  color: #a16bff;
  clear: none;
  cursor: pointer;
  display: inline-block;
  line-height: 0;
  height: 23px;
  margin: -0.25rem 0.25rem 0 0;
  outline: 0;
  text-align: center;
  vertical-align: middle;
  width: 23px;
  min-width: 23px;
  -webkit-appearance: none;
  transition: 0.05s border-color ease-in-out;
}

.better-payment-setup-final-info input[type=checkbox]:not(:checked) {
  border: 3px solid red;
}

.better-payment-setup-final-info input[type=checkbox]:checked::before {
  color: #a16bff;
  margin: -9px 0 0 -8px;
  width: 16px;
  content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 26 26' fill='%23a16bff'%3E%3Cpath d='M 22.566406 4.730469 L 20.773438 3.511719 C 20.277344 3.175781 19.597656 3.304688 19.265625 3.796875 L 10.476563 16.757813 L 6.4375 12.71875 C 6.015625 12.296875 5.328125 12.296875 4.90625 12.71875 L 3.371094 14.253906 C 2.949219 14.675781 2.949219 15.363281 3.371094 15.789063 L 9.582031 22 C 9.929688 22.347656 10.476563 22.613281 10.96875 22.613281 C 11.460938 22.613281 11.957031 22.304688 12.277344 21.839844 L 22.855469 6.234375 C 23.191406 5.742188 23.0625 5.066406 22.566406 4.730469 Z'%3E%3C/path%3E%3C/svg%3E");
}

.better-payment-setup-final-info label {
  font-size: 16px;
  line-height: 1.2em;
  color: #5a5a5a;
  font-weight: 400;
}

.better-payment-setup-final-info button {
  text-decoration: none;
  border: 0;
  padding: 0;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 15px;
  line-height: 1.2em;
  color: #252525;
  font-weight: 400;
}

.better-payment-setup-final-info button:hover {
  text-decoration: underline;
}

/* integrations */
#integrations .row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 25px;
  padding: 0;
}

#integrations .col-one-third {
  width: 100%;
}

#integrations .better-payment-admin-block-wrapper {
  margin: 0;
}

#integrations .better-payment-admin-block-wrapper .better-payment-admin-block {
  width: auto;
  margin: 0;
}

/* templately */
#templately {
  position: relative;
  min-height: 300px;
}

/* elements */
#elements .row {
  padding: 0;
}

.better-payment-elements-cat {
  font-size: 18px;
  line-height: 1.4em;
  color: #252525;
  font-weight: 400;
  position: relative;
  margin: 0 0 13px;
}

.better-payment-elements-cat:before {
  content: "";
  position: absolute;
  left: -30px;
  width: 2px;
  height: 100%;
  background-color: #6e2dcf;
}

.setup-content .better-payment-checkbox-container {
  margin: -5px;
}

.better-payment-setup-wizard-wrap .setup-content .better-payment-checkbox {
  flex-basis: 18.7% !important;
  font-size: 14px;
  line-height: 1.2em;
  color: #333;
  font-weight: 500;
  margin: 5px;
  transition: none;
  box-shadow: none;
}

.better-payment-setup-wizard-wrap .setup-content .better-payment-checkbox {
  padding: 0px;
}

.better-payment-checkbox input[type=checkbox].better-payment-element {
  visibility: visible !important;
  padding: 6.5px !important;
  border: 1px solid #d7d7d7;
  background-color: #fff;
  box-shadow: none !important;
  border-radius: 2px;
  color: #a16bff;
  clear: none;
  cursor: pointer;
  display: inline-block;
  line-height: 0;
  height: 13px;
  margin: 0;
  outline: 0;
  text-align: center;
  vertical-align: middle;
  width: 13px;
  min-width: 13px;
  -webkit-appearance: none;
  transition: 0.05s border-color ease-in-out;
}

.better-payment-checkbox label.better-payment-element-title {
  width: 100%;
  height: unset;
  font-size: 14px;
  line-height: 1.2em;
  color: #333;
  font-weight: 500;
  margin: 0 0 0 10px;
}

.better-payment-checkbox label.better-payment-element-title:before,
.better-payment-checkbox label.better-payment-element-title:after {
  content: none;
}

.better-payment-checkbox input[type=checkbox].better-payment-element:checked {
  background-color: #a16bff;
  border-color: #a16bff;
}

.better-payment-checkbox input[type=checkbox].better-payment-element:checked::before {
  color: #ffffff;
  margin: -5px 0 0 -5px;
  width: 10px;
  content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 26 26' fill='%23ffffff'%3E%3Cpath d='M 22.566406 4.730469 L 20.773438 3.511719 C 20.277344 3.175781 19.597656 3.304688 19.265625 3.796875 L 10.476563 16.757813 L 6.4375 12.71875 C 6.015625 12.296875 5.328125 12.296875 4.90625 12.71875 L 3.371094 14.253906 C 2.949219 14.675781 2.949219 15.363281 3.371094 15.789063 L 9.582031 22 C 9.929688 22.347656 10.476563 22.613281 10.96875 22.613281 C 11.460938 22.613281 11.957031 22.304688 12.277344 21.839844 L 22.855469 6.234375 C 23.191406 5.742188 23.0625 5.066406 22.566406 4.730469 Z'%3E%3C/path%3E%3C/svg%3E");
}

/* footer */
.better-payment-setup-footer {
  text-align: right;
  min-height: unset !important;
  background: unset !important;
}

.better-payment-setup-wizard-wrap .better-payment-btn {
  width: 140px;
  text-transform: capitalize !important;
}

.better-payment-setup-wizard-wrap .button.better-payment-btn:focus,
.better-payment-setup-wizard-wrap .button.better-payment-btn:hover {
  background-color: #02cc7b;
}

button#better-payment-save[disabled] {
  cursor: wait;
  background-image: -webkit-linear-gradient(-169deg, rgb(146, 113, 255) 0%, rgb(87, 37, 255) 100%) !important;
}

#better-payment-next,
#better-payment-save {
  background-image: -webkit-linear-gradient(-169deg, rgb(146, 113, 255) 0%, rgb(87, 37, 255) 100%);
}

#better-payment-prev {
  margin-right: 10px;
  background-color: #7d81ae;
}

/* config */
.better-payment-input-group {
  display: block;
  position: relative;
  margin-bottom: 10px;
}

.config-list label {
  padding: 20px;
  display: block;
  text-align: left;
  color: #3C454C;
  cursor: pointer;
  position: relative;
  z-index: 2;
  transition: color 200ms ease-in;
  overflow: hidden;
  border-radius: 5px;
  background-color: #fff;
  border: 1px solid #e1e1e1;
}

.config-list input[type=radio] {
  display: none;
}

.config-list input:checked ~ label {
  box-shadow: 1px 7px 30px rgba(0, 1, 35, 0.12);
}

.config-list input:checked ~ label .better-payment-radio-circle {
  border-color: #6e2dcf;
  box-shadow: 0px 40px 30px rgba(0, 1, 35, 0.12);
}

.config-list input:checked ~ label .better-payment-radio-circle:before {
  content: "";
  background: #6e2dcf;
  border-radius: 50%;
  height: 10px;
  left: 1px;
  pointer-events: none;
  position: absolute;
  top: 1px;
  transition: transform 400ms cubic-bezier(0.175, 0.885, 0.32, 1.2);
  width: 10px;
}

.better-payment-radio-circle {
  border: 2px solid #cccccc;
  border-radius: 50%;
  cursor: pointer;
  height: 16px;
  position: absolute;
  transition: border-color 300ms;
  width: 16px;
}

.better-payment-radio-text {
  margin-left: 35px;
}

.better-payment-radio-text strong {
  font-size: 18px;
  line-height: 1.2em;
  color: #252525;
  font-weight: 400;
  vertical-align: top;
}

.better-payment-radio-text p {
  margin: 5px 0 0;
  font-size: 15px;
  line-height: 1.4em;
  color: #5a5a5a;
  font-weight: 400;
}

.better-payment .quick-setup-paypal,
.better-payment .quick-setup-stripe {
  border: 1px solid #e1e1e1;
  border-radius: 5px;
  box-shadow: 1px 7px 30px rgba(0, 1, 35, 0.12);
}

/* end setup wizard */
