const path = require("path");
const glob = require("glob");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const wpPot = require("wp-pot");
const outputEntry = () => {
	let paths = {};

	glob.sync("./src/js/*").reduce((acc, file) => {
		let fileName = path.parse(file).name;

		if (fileName.charAt(0) !== "_") {
			if(fileName != 'elementor'){
				paths[path.join("js", "", fileName)] = file;
			}
		}
	}, {});

	glob.sync("./src/js/elementor/edit/*").reduce((acc, file) => {
		let fileName = path.parse(file).name;

		if (fileName.charAt(0) !== "_") {
			paths[path.join("js", "elementor/edit", fileName)] = file;
		}
	}, {});

	glob.sync("./src/css/*").reduce((acc, file) => {
		let fileName = path.parse(file).name;

		if (fileName.charAt(0) !== "_") {
			paths[path.join("css", "", fileName)] = file;
		}
	}, {});

	return paths;
};
const removeEntry = () => {
	entry = [];

	glob.sync("./src/css/*").reduce((acc, file) => {
		let fileName = path.parse(file).name;

		if (fileName.charAt(0) !== "_") {
			entry.push(path.join("css", "", fileName.concat(".js")));
			entry.push(path.join("css", "", fileName.concat(".min.js")));
		}
	}, {});

	return entry;
};

module.exports = (env, argv) => {
	if (argv.mode === "production") {
		// Generate .pot on production build only
		wpPot({
			destFile: "languages/better-payment.pot",
			domain: "better-payment",
			package: "Better Payment",
			includePOTCreationDate: false,
			src: "**/*.php",
		});
	}

	return {
		stats: "minimal",
		entry: outputEntry(),
		output: {
			path: path.resolve(__dirname, "assets/"),
			filename:
				argv.mode === "production" ? "[name].min.js" : "[name].js",
		},
		plugins: [
			new MiniCssExtractPlugin({
				filename:
					argv.mode === "production"
						? "[name].min.css"
						: "[name].css",
			}),
			{
				apply(compiler) {
					compiler.hooks.shouldEmit.tap(
						"removeStylesFromOutput",
						(compilation) => {
							removeEntry(argv).forEach((entry) => {
								delete compilation.assets[entry];
							});
							return true;
						}
					);
				},
			},
		],
		module: {
			rules: [
				{
					test: /\.(js)$/,
					exclude: /node_modules/,
					use: {
						loader: "babel-loader",
					},
				},
				{
					test: /\.(scss)$/,
					use: [
						MiniCssExtractPlugin.loader,
						{ loader: "css-loader", options: { url: false } },
						"postcss-loader",
						"sass-loader",
					],
				},
			],
		},
	};
};
